{% extends 'estoque/base.html' %}

{% block title %}Relatório de Estoque - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Relatório de Estoque</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-3">
                    <label for="{{ form.cliente.id_for_label }}" class="form-label">Cliente</label>
                    {{ form.cliente.errors }}
                    <input type="text" name="{{ form.cliente.name }}" id="{{ form.cliente.id_for_label }}" class="form-control" value="{{ form.cliente.value|default:'' }}">
                </div>

                <div class="col-md-3">
                    <label for="{{ form.codigo.id_for_label }}" class="form-label"><PERSON><PERSON><PERSON> da <PERSON></label>
                    {{ form.codigo.errors }}
                    <input type="text" name="{{ form.codigo.name }}" id="{{ form.codigo.id_for_label }}" class="form-control" value="{{ form.codigo.value|default:'' }}">
                </div>

                <div class="col-md-3">
                    <label for="{{ form.ordenacao.id_for_label }}" class="form-label">Ordenar por</label>
                    {{ form.ordenacao.errors }}
                    <select name="{{ form.ordenacao.name }}" id="{{ form.ordenacao.id_for_label }}" class="form-select">
                        {% for value, text in form.fields.ordenacao.choices %}
                            <option value="{{ value }}" {% if form.ordenacao.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-3 d-flex align-items-end">
                    <div class="form-check">
                        <input type="checkbox" name="{{ form.estoque_baixo.name }}" id="{{ form.estoque_baixo.id_for_label }}" class="form-check-input" {% if form.estoque_baixo.value %}checked{% endif %}>
                        <label for="{{ form.estoque_baixo.id_for_label }}" class="form-check-label">Apenas Estoque Baixo</label>
                    </div>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.formato_relatorio.id_for_label }}" class="form-label">Formato do Relatório</label>
                    {{ form.formato_relatorio.errors }}
                    <select name="{{ form.formato_relatorio.name }}" id="{{ form.formato_relatorio.id_for_label }}" class="form-select">
                        {% for value, text in form.fields.formato_relatorio.choices %}
                            <option value="{{ value }}" {% if form.formato_relatorio.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>

                    <div class="float-end">
                        <button type="submit" name="formato" value="pdf" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> Exportar PDF
                        </button>
                        <button type="submit" name="formato" value="csv" class="btn btn-success">
                            <i class="fas fa-file-csv"></i> Exportar CSV
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% if molas %}
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    Relatório de Estoque
                    {% if filtro_cliente %} - Cliente: {{ filtro_cliente }}{% endif %}
                    {% if filtro_codigo %} - Código: {{ filtro_codigo }}{% endif %}
                    {% if filtro_estoque_baixo %} - Apenas Estoque Baixo{% endif %}
                </h6>
                <button onclick="window.print()" class="btn btn-sm btn-secondary">
                    <i class="fas fa-print"></i> Imprimir
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Cliente</th>
                                <th>Material</th>
                                <th>Diâmetro</th>
                                <th>Estoque Atual</th>
                                <th>Estoque Mínimo</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mola in molas %}
                                <tr>
                                    <td>{{ mola.codigo }}</td>
                                    <td>{{ mola.cliente }}</td>
                                    <td>{% if mola.material %}{{ mola.material.nome }}{% else %}-{% endif %}</td>
                                    <td>{% if mola.diametro %}{{ mola.diametro }}{% elif mola.material and mola.material.diametro %}{{ mola.material.diametro }}{% else %}-{% endif %}</td>
                                    <td>{{ mola.quantidade_estoque }}</td>
                                    <td>{{ mola.estoque_minimo }}</td>
                                    <td>
                                        {% if mola.quantidade_estoque <= mola.estoque_minimo %}
                                            <span class="badge bg-danger">Estoque Baixo</span>
                                        {% else %}
                                            <span class="badge bg-success">OK</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'mola-detail' mola.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Informações da Mola">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <p><strong>Total de Molas:</strong> {{ molas|length }}</p>
                    <p><strong>Total em Estoque Baixo:</strong> {{ estoque_baixo_count }}</p>
                </div>
            </div>
        </div>
    {% elif request.method == 'POST' %}
        <div class="alert alert-info alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            Nenhuma mola encontrada com os filtros selecionados.
        </div>
    {% endif %}
</div>

<style>
    @media print {
        .navbar, .sidebar, .footer, .card-header button, form, .btn {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            color: black !important;
            border-bottom: 1px solid #ddd !important;
        }

        body {
            background-color: white !important;
            color: black !important;
        }

        .table {
            color: black !important;
        }

        .badge {
            border: 1px solid #ddd !important;
        }

        .badge.bg-danger {
            background-color: white !important;
            color: black !important;
            border-color: #dc3545 !important;
        }

        .badge.bg-success {
            background-color: white !important;
            color: black !important;
            border-color: #28a745 !important;
        }
    }
</style>
{% endblock %}
