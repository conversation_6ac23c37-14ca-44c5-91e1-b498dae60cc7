from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, F, Q, Avg, Case, When, Value, IntegerField, Prefetch, OuterRef, Subquery
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from django.core.cache import cache
from datetime import timedelta, date, datetime
from decimal import Decimal
import json
import logging
import os
import psutil
import time

from .models import (
    MaterialPadrao, Material, Mola, MovimentacaoEstoque, MovimentacaoMaterial,
    PedidoVenda, ItemPedido, PrevisaoDemanda, AnaliseObsolescencia,
    ItemPlanejamento
)

# Importar módulo de previsão avançada
try:
    from .forecasting import (
        generate_forecast, generate_all_forecasts,
        STATSMODELS_INSTALLED, PROPHET_INSTALLED, SKLEARN_INSTALLED
    )
    ADVANCED_FORECASTING_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("Módulo de previsão avançada carregado com sucesso.")
except ImportError as e:
    ADVANCED_FORECASTING_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"Não foi possível carregar o módulo de previsão avançada: {str(e)}")
    logger.warning("Usando métodos de previsão básicos.")
from .forms import (MaterialPadraoForm, MaterialForm, MolaForm, MovimentacaoEstoqueForm, MovimentacaoMaterialForm,
                   PedidoVendaForm, ItemPedidoForm, RelatorioMolasForm, RelatorioEstoqueForm,
                   MovimentacaoMultiplaForm, RelatorioVendasPorMolaForm, RelatorioMolasNaoVendidasForm)
from .utils import ordenar_materiais, extrair_valor_numerico_diametro, extrair_valor_numerico_nome_mola
from .filters import (MolaFilter, MaterialFilter, MovimentacaoEstoqueFilter,
                     MovimentacaoMaterialFilter, PedidoVendaFilter)

import io
import csv
import re
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer, Image as RLImage, KeepTogether
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm

# Configuração crítica do matplotlib para evitar problemas de threading
import os
os.environ['MPLBACKEND'] = 'Agg'

try:
    import matplotlib
    matplotlib.use('Agg', force=True)
    import matplotlib.pyplot as plt
    plt.ioff()  # Desativar modo interativo
    matplotlib.rcParams['backend'] = 'Agg'
    matplotlib.rcParams['interactive'] = False
except Exception as e:
    print(f"Aviso: Erro ao configurar matplotlib: {e}")
    import matplotlib.pyplot as plt

import numpy as np
from .report_utils import get_styles, add_header_footer, create_table_style, create_chart, create_line_chart


# Função para monitorar uso de recursos
def get_system_resources():
    """Obtém informações sobre uso de recursos do sistema"""
    process = psutil.Process(os.getpid())

    # Uso de memória
    memory_info = process.memory_info()
    memory_usage_mb = memory_info.rss / 1024 / 1024  # Converter para MB

    # Uso de CPU
    cpu_percent = process.cpu_percent(interval=0.1)

    # Número de threads
    num_threads = process.num_threads()

    # Tempo de execução
    process_time = process.create_time()
    uptime_seconds = time.time() - process_time

    return {
        'memory_usage_mb': round(memory_usage_mb, 2),
        'cpu_percent': round(cpu_percent, 2),
        'num_threads': num_threads,
        'uptime_hours': round(uptime_seconds / 3600, 2)
    }

# Views para Dashboard
@cache_page(60 * 5)  # Cache por 5 minutos
def dashboard(request):
    """View para o dashboard principal"""
    # Usar cache para consultas frequentes
    cache_key = 'dashboard_data'
    dashboard_data = cache.get(cache_key)

    if not dashboard_data:
        logger.info("Gerando dados do dashboard (cache miss)")

        # Otimizar consultas usando agregações
        # Fazer contagens e alertas em uma única consulta para cada modelo
        molas_stats = Mola.objects.aggregate(
            total=Count('id'),
            estoque_baixo=Count('id', filter=Q(quantidade_estoque__lte=F('estoque_minimo')))
        )

        materiais_stats = Material.objects.aggregate(
            total=Count('id'),
            estoque_baixo=Count('id', filter=Q(quantidade_estoque__lte=F('estoque_minimo')))
        )

        # Movimentações recentes - usar select_related para otimizar consultas
        # Limitar campos retornados para melhorar desempenho
        movimentacoes_recentes = list(
            MovimentacaoEstoque.objects.select_related('mola')
            .only('id', 'tipo', 'quantidade', 'data', 'mola__codigo', 'mola__cliente')
            .order_by('-data')[:10]
        )

        # Molas mais vendidas (últimos 30 dias)
        molas_mais_vendidas = list(Mola.mais_vendidas(periodo='mes')[:5])

        dashboard_data = {
            'total_molas': molas_stats['total'],
            'total_materiais': materiais_stats['total'],
            'molas_estoque_baixo': molas_stats['estoque_baixo'],
            'materiais_estoque_baixo': materiais_stats['estoque_baixo'],
            'movimentacoes_recentes': movimentacoes_recentes,
            'molas_mais_vendidas': molas_mais_vendidas,
            'timestamp': timezone.now()
        }

        # Armazenar no cache por 5 minutos
        cache.set(cache_key, dashboard_data, 60 * 5)
    else:
        logger.info("Usando dados do dashboard em cache (cache hit)")

    # Adicionar informações de recursos do sistema
    try:
        system_resources = get_system_resources()
        dashboard_data.update(system_resources)
    except Exception as e:
        logger.error(f"Erro ao obter recursos do sistema: {str(e)}")

    return render(request, 'estoque/dashboard.html', dashboard_data)


def dashboard_kpi(request):
    """Dashboard avançado com KPIs e gráficos"""
    # Obter parâmetros de filtro
    periodo = request.GET.get('periodo', '30')
    cliente = request.GET.get('cliente', '')
    categoria = request.GET.get('categoria', '')

    # Converter período para inteiro
    try:
        dias = int(periodo)
    except ValueError:
        dias = 30

    # Calcular datas
    hoje = timezone.now().date()
    data_inicio = hoje - timezone.timedelta(days=dias)
    data_inicio_anterior = data_inicio - timezone.timedelta(days=dias)  # Período anterior para comparação

    # Filtrar movimentações por período
    movimentacoes = MovimentacaoEstoque.objects.filter(data__gte=data_inicio)
    movimentacoes_anterior = MovimentacaoEstoque.objects.filter(
        data__gte=data_inicio_anterior,
        data__lt=data_inicio
    )

    # Filtrar por cliente se especificado
    if cliente:
        movimentacoes = movimentacoes.filter(mola__cliente__icontains=cliente)
        movimentacoes_anterior = movimentacoes_anterior.filter(mola__cliente__icontains=cliente)

    # Filtrar por categoria se especificada
    if categoria:
        # Obter molas da categoria especificada
        analises = AnaliseObsolescencia.objects.filter(classificacao=categoria)
        molas_ids = analises.values_list('mola_id', flat=True)

        movimentacoes = movimentacoes.filter(mola_id__in=molas_ids)
        movimentacoes_anterior = movimentacoes_anterior.filter(mola_id__in=molas_ids)

    # Calcular KPIs

    # 1. Rotatividade de Estoque
    # Fórmula: Vendas no período / Estoque médio
    vendas_periodo = movimentacoes.filter(tipo='S').aggregate(total=Sum('quantidade'))['total'] or 0
    vendas_anterior = movimentacoes_anterior.filter(tipo='S').aggregate(total=Sum('quantidade'))['total'] or 0

    estoque_atual = Mola.objects.aggregate(total=Sum('quantidade_estoque'))['total'] or 0

    # Estimar estoque no início do período (estoque atual + saídas - entradas)
    entradas_periodo = movimentacoes.filter(tipo='E').aggregate(total=Sum('quantidade'))['total'] or 0
    saidas_periodo = vendas_periodo
    estoque_inicio = estoque_atual + saidas_periodo - entradas_periodo

    # Estoque médio no período
    estoque_medio = (estoque_atual + estoque_inicio) / 2 if (estoque_atual + estoque_inicio) != 0 else estoque_atual

    # Calcular rotatividade
    rotatividade_estoque = vendas_periodo / estoque_medio if estoque_medio > 0 else 0

    # Calcular tendência (comparação com período anterior)
    rotatividade_anterior = vendas_anterior / estoque_medio if estoque_medio > 0 else 0
    rotatividade_tendencia = ((rotatividade_estoque - rotatividade_anterior) / rotatividade_anterior * 100) if rotatividade_anterior > 0 else 0
    rotatividade_tendencia_abs = abs(rotatividade_tendencia) if rotatividade_tendencia < 0 else rotatividade_tendencia

    # 2. Cobertura de Estoque
    # Fórmula: Estoque atual / (Vendas no período / Número de dias)
    vendas_diarias = vendas_periodo / dias if dias > 0 else 0
    cobertura_estoque = estoque_atual / vendas_diarias if vendas_diarias > 0 else 0

    # 3. Taxa de Atendimento
    # Percentual de pedidos atendidos completamente
    pedidos_periodo = PedidoVenda.objects.filter(data_pedido__gte=data_inicio)

    if cliente:
        pedidos_periodo = pedidos_periodo.filter(cliente__icontains=cliente)

    total_pedidos = pedidos_periodo.count()
    pedidos_atendidos = pedidos_periodo.annotate(
        itens_total=Count('itens'),
        itens_atendidos=Count('itens', filter=Q(itens__atendido=True))
    ).filter(itens_total=F('itens_atendidos')).count()

    taxa_atendimento = (pedidos_atendidos / total_pedidos * 100) if total_pedidos > 0 else 0

    # 4. Valor em Estoque
    # Valor total do estoque atual (usando custo médio estimado)
    custo_medio = 0.001  # Valor estimado por unidade
    valor_estoque = estoque_atual * custo_medio

    # Valor do estoque no período anterior
    valor_estoque_anterior = estoque_inicio * custo_medio
    valor_estoque_tendencia = ((valor_estoque - valor_estoque_anterior) / valor_estoque_anterior * 100) if valor_estoque_anterior > 0 else 0
    valor_estoque_tendencia_abs = abs(valor_estoque_tendencia) if valor_estoque_tendencia < 0 else valor_estoque_tendencia

    # Dados para gráficos

    # 1. Tendência de Vendas
    # Agrupar vendas por dia/semana/mês
    vendas_diarias_dict = {}

    # Inicializar com zeros para todos os dias no período
    for i in range(dias):
        data = hoje - timezone.timedelta(days=i)
        vendas_diarias_dict[data.strftime('%Y-%m-%d')] = 0

    # Preencher com dados reais
    vendas_por_dia = movimentacoes.filter(tipo='S').values('data').annotate(
        total=Sum('quantidade')
    ).order_by('data')

    for venda in vendas_por_dia:
        data_str = venda['data'].strftime('%Y-%m-%d')
        vendas_diarias_dict[data_str] = venda['total']

    # Ordenar por data
    vendas_labels = []
    vendas_dados = []

    for data_str, quantidade in sorted(vendas_diarias_dict.items()):
        vendas_labels.append(data_str)
        vendas_dados.append(quantidade)

    # Gerar previsão simples para os próximos 7 dias
    vendas_previsao = [None] * (len(vendas_dados) - 7)  # Nulos para dados históricos

    # Calcular média móvel dos últimos 7 dias para previsão
    if len(vendas_dados) >= 7:
        media_movel = sum(vendas_dados[-7:]) / 7
        for _ in range(7):
            vendas_previsao.append(media_movel)

    # 2. Distribuição por Categoria
    categorias_dados = [0, 0, 0, 0, 0]  # A, B, C, D, O

    # Contar molas por categoria em uma única consulta
    categorias_count = AnaliseObsolescencia.objects.values('classificacao').annotate(
        count=Count('id')
    ).order_by('classificacao')

    # Mapear os resultados para o array
    categorias_map = {'A': 0, 'B': 1, 'C': 2, 'D': 3, 'O': 4}
    for item in categorias_count:
        if item['classificacao'] in categorias_map:
            categorias_dados[categorias_map[item['classificacao']]] = item['count']

    # 3. Top 5 Molas
    top_molas = Mola.mais_vendidas(periodo=periodo)[:5]

    # Calcular percentual do total
    total_vendido = sum(mola['total_vendido'] for mola in top_molas)

    for mola in top_molas:
        mola['percentual'] = (mola['total_vendido'] / total_vendido * 100) if total_vendido > 0 else 0

    # 4. Alertas e Recomendações
    alertas = []

    # Consultar todos os alertas em uma única consulta
    # Alerta de estoque baixo e itens obsoletos
    molas_estoque_baixo = Mola.objects.filter(quantidade_estoque__lte=F('estoque_minimo')).count()
    itens_obsoletos = categorias_count.filter(classificacao='O').first()
    itens_obsoletos_count = itens_obsoletos['count'] if itens_obsoletos else 0

    if molas_estoque_baixo > 0:
        alertas.append({
            'titulo': f'{molas_estoque_baixo} molas com estoque abaixo do mínimo',
            'descricao': 'Verifique as molas com estoque abaixo do mínimo e crie ordens de fabricação.',
            'nivel': 'alto' if molas_estoque_baixo > 5 else 'medio',
            'data': hoje,
            'acao': 'Ver Molas',
            'url': '/estoque/alertas/estoque/'
        })

    if itens_obsoletos_count > 0:
        alertas.append({
            'titulo': f'{itens_obsoletos_count} itens classificados como obsoletos',
            'descricao': 'Considere estratégias para reduzir o estoque de itens obsoletos.',
            'nivel': 'medio',
            'data': hoje,
            'acao': 'Ver Análise',
            'url': '/analise-obsolescencia/'
        })

    # Alerta de cobertura de estoque baixa
    if cobertura_estoque < 15:  # Menos de 15 dias de cobertura
        alertas.append({
            'titulo': 'Cobertura de estoque baixa',
            'descricao': f'A cobertura média de estoque é de apenas {cobertura_estoque:.0f} dias.',
            'nivel': 'alto' if cobertura_estoque < 7 else 'medio',
            'data': hoje,
            'acao': 'Criar Ordem de Fabricação',
            'url': '/planejamento/'
        })

    # Alerta de taxa de atendimento baixa
    if taxa_atendimento < 90:  # Menos de 90% de atendimento
        alertas.append({
            'titulo': 'Taxa de atendimento abaixo do ideal',
            'descricao': f'Apenas {taxa_atendimento:.1f}% dos pedidos foram atendidos completamente.',
            'nivel': 'alto' if taxa_atendimento < 75 else 'medio',
            'data': hoje,
            'acao': 'Ver Pedidos',
            'url': '/pedidos/'
        })

    context = {
        'periodo': periodo,
        'cliente': cliente,
        'categoria': categoria,
        'rotatividade_estoque': rotatividade_estoque,
        'rotatividade_tendencia': rotatividade_tendencia,
        'rotatividade_tendencia_abs': rotatividade_tendencia_abs,
        'cobertura_estoque': cobertura_estoque,
        'taxa_atendimento': taxa_atendimento,
        'valor_estoque': valor_estoque,
        'valor_estoque_tendencia': valor_estoque_tendencia,
        'valor_estoque_tendencia_abs': valor_estoque_tendencia_abs,
        'vendas_labels': json.dumps(vendas_labels),
        'vendas_dados': json.dumps(vendas_dados),
        'vendas_previsao': json.dumps(vendas_previsao),
        'categorias_dados': json.dumps(categorias_dados),
        'top_molas': top_molas,
        'alertas': alertas,
    }

    return render(request, 'estoque/dashboard_kpi.html', context)


def dashboard_executivo(request):
    """Dashboard executivo com KPIs avançados de business intelligence"""
    try:
        # Cache para performance
        cache_key = 'dashboard_executivo_data'
        dashboard_data = cache.get(cache_key)

        if dashboard_data is None:
            # KPIs principais
            hoje = timezone.now().date()
            mes_atual = hoje.replace(day=1)
            mes_anterior = (mes_atual - timedelta(days=1)).replace(day=1)

            # Vendas do mês atual vs anterior
            vendas_mes_atual = MovimentacaoEstoque.objects.filter(
                tipo='S',
                data__gte=mes_atual
            ).aggregate(total=Sum('quantidade'))['total'] or 0

            vendas_mes_anterior = MovimentacaoEstoque.objects.filter(
                tipo='S',
                data__gte=mes_anterior,
                data__lt=mes_atual
            ).aggregate(total=Sum('quantidade'))['total'] or 0

            # Crescimento MoM
            if vendas_mes_anterior > 0:
                crescimento_mom = ((vendas_mes_atual - vendas_mes_anterior) / vendas_mes_anterior) * 100
            else:
                crescimento_mom = 100 if vendas_mes_atual > 0 else 0

            # Top 5 molas com KPIs avançados
            top_molas = []
            molas_vendidas = Mola.mais_vendidas(periodo='mes')[:5]

            for mola_data in molas_vendidas:
                mola_id = mola_data['mola']
                inventory_turnover = Mola.calcular_inventory_turnover(mola_id)
                dso = Mola.calcular_days_sales_outstanding(mola_id)

                top_molas.append({
                    'codigo': mola_data['mola_codigo'],
                    'cliente': mola_data['mola_cliente'],
                    'vendas': mola_data['total_vendido'],
                    'inventory_turnover': inventory_turnover,
                    'dso': dso,
                    'status_turnover': 'high' if inventory_turnover > 6 else 'medium' if inventory_turnover > 2 else 'low'
                })

            # Alertas automáticos
            alertas = []

            # Alerta de estoque baixo
            molas_estoque_baixo = Mola.objects.filter(
                quantidade_estoque__lte=F('estoque_minimo')
            ).count()

            if molas_estoque_baixo > 0:
                alertas.append({
                    'tipo': 'warning',
                    'titulo': 'Estoque Baixo',
                    'mensagem': f'{molas_estoque_baixo} molas com estoque abaixo do mínimo',
                    'acao': 'Ver Relatório de Estoque'
                })

            # Alerta de queda nas vendas
            if crescimento_mom < -10:
                alertas.append({
                    'tipo': 'danger',
                    'titulo': 'Queda nas Vendas',
                    'mensagem': f'Vendas caíram {abs(crescimento_mom):.1f}% em relação ao mês anterior',
                    'acao': 'Analisar Tendências'
                })

            dashboard_data = {
                'vendas_mes_atual': vendas_mes_atual,
                'vendas_mes_anterior': vendas_mes_anterior,
                'crescimento_mom': round(crescimento_mom, 1),
                'top_molas': top_molas,
                'alertas': alertas,
                'timestamp': timezone.now()
            }

            # Cache por 30 minutos
            cache.set(cache_key, dashboard_data, 1800)

        return render(request, 'estoque/dashboard_executivo.html', dashboard_data)

    except Exception as e:
        messages.error(request, f'Erro ao carregar dashboard executivo: {str(e)}')
        return redirect('dashboard')


# Views para Molas
class MolaListView(ListView):
    model = Mola
    template_name = 'estoque/mola_list.html'
    context_object_name = 'molas'
    paginate_by = 20  # Aumentar para reduzir número de requisições de paginação

    def get_queryset(self):
        # Otimizar consultas usando select_related e only para limitar campos
        queryset = (
            super().get_queryset()
            .select_related('material', 'material_padrao')
            .only(
                'id', 'codigo', 'nome_mola', 'cliente', 'quantidade_estoque',
                'estoque_minimo', 'material__nome', 'material__diametro',
                'material_padrao__nome', 'ativo'
            )
        )

        # Aplicar filtros
        self.filterset = MolaFilter(self.request.GET, queryset=queryset)
        filtered_qs = self.filterset.qs

        # Aplicar ordenação numérica por nome_mola
        molas_list = list(filtered_qs)
        molas_ordenadas = sorted(molas_list, key=lambda m: extrair_valor_numerico_nome_mola(m.nome_mola or m.codigo))

        return molas_ordenadas

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset

        # Adicionar timestamp para mostrar quando os dados foram atualizados
        context['timestamp'] = timezone.now()

        # Adicionar informações de recursos do sistema
        try:
            context['system_resources'] = get_system_resources()
        except Exception as e:
            logger.error(f"Erro ao obter recursos do sistema: {str(e)}")

        # Adicionar contadores para o cabeçalho
        context['total_molas'] = self.filterset.qs.count()
        context['estoque_baixo_count'] = self.filterset.qs.filter(
            quantidade_estoque__lte=F('estoque_minimo')
        ).count()

        return context

    def dispatch(self, request, *args, **kwargs):
        # Desativar cache para esta view para garantir dados atualizados
        response = super().dispatch(request, *args, **kwargs)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response


class MolaDetailView(DetailView):
    model = Mola
    template_name = 'estoque/mola_detail.html'
    context_object_name = 'mola'

    def get_queryset(self):
        # Otimizar consulta usando select_related
        return super().get_queryset().select_related('material')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mola = self.get_object()

        # Histórico de movimentações - otimizar consulta
        # Removido select_related('mola') pois já estamos filtrando por mola.movimentacoes.all()
        context['movimentacoes'] = (
            mola.movimentacoes.all()
            .only('id', 'tipo', 'quantidade', 'data', 'ordem_venda', 'observacao')
            .order_by('-data')[:10]
        )

        # Verificar se há material suficiente para produção
        if mola.verificar_estoque_minimo() and mola.material:
            # Se estiver abaixo do mínimo e tiver material associado, calcular necessidade de material
            quantidade_necessaria = mola.estoque_minimo - mola.quantidade_estoque
            context['necessidade_material'] = mola.verificar_material_suficiente(quantidade_necessaria)

            # Adicionar informação sobre prazo de reposição
            context['dias_para_reposicao'] = 7  # Valor estimado

        # Calcular consumo total de material para o estoque atual
        if mola.peso_unitario and mola.material:
            # Converter de gramas para quilogramas (dividir por 1000)
            context['consumo_total'] = float(mola.peso_unitario) / 1000 * mola.quantidade_estoque

        # Adicionar estatísticas de vendas
        hoje = timezone.now().date()
        data_inicio = hoje - timezone.timedelta(days=30)

        vendas_recentes = (
            mola.movimentacoes
            .filter(tipo='S', data__gte=data_inicio)
            .aggregate(
                total=Sum('quantidade'),
                media_diaria=Avg('quantidade')
            )
        )

        context['vendas_recentes'] = vendas_recentes

        # Calcular tempo estimado para esgotar o estoque
        if vendas_recentes['media_diaria'] and vendas_recentes['media_diaria'] > 0:
            context['dias_ate_esgotar'] = int(mola.quantidade_estoque / vendas_recentes['media_diaria'])

        return context


class MolaCreateView(CreateView):
    model = Mola
    form_class = MolaForm
    template_name = 'estoque/mola_form.html'
    success_url = reverse_lazy('mola-list')

    def form_valid(self, form):
        # Verificar se o valor do peso_unitario foi enviado no formulário
        peso_unitario_raw = self.request.POST.get('peso_unitario')

        if peso_unitario_raw:
            # Converter o valor do formulário (substituindo vírgula por ponto)
            from decimal import Decimal
            try:
                peso_unitario_raw = peso_unitario_raw.replace(',', '.')
                form.instance.peso_unitario = Decimal(peso_unitario_raw).quantize(Decimal('0.0001'))
            except:
                # Se houver erro na conversão, usar o valor processado pelo formulário
                if 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
                    form.instance.peso_unitario = form.cleaned_data['peso_unitario']
        elif 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
            # Usar o valor processado pelo formulário
            form.instance.peso_unitario = form.cleaned_data['peso_unitario']

        messages.success(self.request, 'Mola cadastrada com sucesso!')
        return super().form_valid(form)


class MolaUpdateView(UpdateView):
    model = Mola
    form_class = MolaForm
    template_name = 'estoque/mola_form.html'
    success_url = reverse_lazy('mola-list')

    def get_object(self, queryset=None):
        """Sobrescreve o método para garantir que o objeto seja carregado corretamente"""
        obj = super().get_object(queryset)
        # Garantir que o objeto seja carregado do banco de dados
        if obj.pk:
            # Recarregar o objeto do banco de dados para garantir que todos os campos estejam atualizados
            obj = Mola.objects.get(pk=obj.pk)
        return obj

    def get_initial(self):
        """Define os valores iniciais para o formulário"""
        initial = super().get_initial()
        # Obter o objeto Mola diretamente do banco de dados
        if self.object:
            # Forçar a inclusão do valor do peso_unitario nos valores iniciais
            if self.object.peso_unitario is not None:
                initial['peso_unitario'] = self.object.peso_unitario
        return initial

    def get_form_kwargs(self):
        """Retorna os kwargs para inicializar o formulário"""
        kwargs = super().get_form_kwargs()
        # Se o objeto existir e tiver um valor para peso_unitario, forçar a inclusão no data
        if self.object and self.object.peso_unitario is not None:
            # Criar uma cópia do data para não modificar o original
            if kwargs.get('data'):
                data = kwargs['data'].copy()
                # Adicionar o valor do peso_unitario ao data
                data[self.get_prefix() + '-peso_unitario' if self.get_prefix() else 'peso_unitario'] = self.object.peso_unitario
                kwargs['data'] = data
        return kwargs

    def get_form(self, form_class=None):
        """Inicializa o formulário com os dados da mola existente"""
        form = super().get_form(form_class)
        # Forçar a definição do valor do campo peso_unitario diretamente no widget
        if self.object and self.object.peso_unitario is not None:
            # Definir o valor diretamente no widget
            form.fields['peso_unitario'].widget.attrs['value'] = self.object.peso_unitario
            # Forçar o valor no campo
            form.initial['peso_unitario'] = self.object.peso_unitario
            # Forçar o valor no data do formulário
            if hasattr(form, 'data') and form.data:
                form.data = form.data.copy()
                form.data[form.add_prefix('peso_unitario')] = str(self.object.peso_unitario)
        return form

    def get_context_data(self, **kwargs):
        """Adiciona dados adicionais ao contexto do template"""
        context = super().get_context_data(**kwargs)
        # Adicionar o valor do peso_unitario diretamente ao contexto
        if self.object and self.object.peso_unitario is not None:
            context['peso_unitario_valor'] = self.object.peso_unitario
        return context

    def form_valid(self, form):
        # Verificar se o valor do peso_unitario foi enviado no formulário
        peso_unitario_raw = self.request.POST.get('peso_unitario')

        if peso_unitario_raw:
            # Converter o valor do formulário (substituindo vírgula por ponto)
            from decimal import Decimal
            try:
                peso_unitario_raw = peso_unitario_raw.replace(',', '.')
                form.instance.peso_unitario = Decimal(peso_unitario_raw).quantize(Decimal('0.0001'))
            except:
                # Se houver erro na conversão, usar o valor processado pelo formulário
                if 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
                    form.instance.peso_unitario = form.cleaned_data['peso_unitario']
        elif 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
            # Usar o valor processado pelo formulário
            form.instance.peso_unitario = form.cleaned_data['peso_unitario']
        # Se o campo estiver vazio mas havia um valor anterior, manter o valor anterior
        elif self.object and self.object.peso_unitario is not None:
            form.instance.peso_unitario = self.object.peso_unitario

        messages.success(self.request, 'Mola atualizada com sucesso!')
        return super().form_valid(form)


class MolaDeleteView(DeleteView):
    model = Mola
    template_name = 'estoque/mola_confirm_delete.html'
    success_url = reverse_lazy('mola-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mola = self.get_object()

        # Verificar se a mola está sendo usada em pedidos
        itens_pedido = ItemPedido.objects.filter(mola=mola)
        context['itens_pedido'] = itens_pedido
        context['tem_pedidos'] = itens_pedido.exists()

        # Verificar se a mola está sendo usada em ordens de fabricação
        itens_planejamento = ItemPlanejamento.objects.filter(mola=mola)
        context['itens_planejamento'] = itens_planejamento
        context['tem_planejamentos'] = itens_planejamento.exists()

        return context

    def post(self, request, *args, **kwargs):
        mola = self.get_object()

        # Verificar se a mola está sendo usada em pedidos ou ordens de fabricação
        itens_pedido = ItemPedido.objects.filter(mola=mola)
        itens_planejamento = ItemPlanejamento.objects.filter(mola=mola)

        # Se não estiver sendo usada, excluir normalmente
        if not itens_pedido.exists() and not itens_planejamento.exists():
            return self.delete(request, *args, **kwargs)

        # Se estiver sendo usada, verificar a ação escolhida pelo usuário
        acao = request.POST.get('acao')

        if acao == 'cancelar':
            messages.info(request, 'Exclusão cancelada.')
            return redirect('mola-detail', pk=mola.id)

        elif acao == 'inativar':
            # Marcar a mola como inativa (soft delete)
            mola.ativo = False
            mola.save()
            messages.success(request, 'Mola marcada como inativa com sucesso!')
            return redirect('mola-list')

        elif acao == 'forcar':
            try:
                # Forçar exclusão (isso vai falhar se houver restrições de chave estrangeira)
                mola.delete()
                messages.success(request, 'Mola excluída com sucesso!')
                return redirect('mola-list')
            except Exception as e:
                messages.error(request, f'Não foi possível excluir a mola: {str(e)}')
                return redirect('mola-detail', pk=mola.id)

        # Se nenhuma ação válida foi escolhida, voltar para a página de confirmação
        messages.error(request, 'Ação inválida.')
        return self.get(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Mola excluída com sucesso!')
        return super().delete(request, *args, **kwargs)


# Views para Materiais Padrão
class MaterialPadraoListView(ListView):
    model = MaterialPadrao
    template_name = 'estoque/material_padrao_list.html'
    context_object_name = 'materiais_padrao'
    paginate_by = 10

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class MaterialPadraoDetailView(DetailView):
    model = MaterialPadrao
    template_name = 'estoque/material_padrao_detail.html'
    context_object_name = 'material_padrao'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material_padrao = self.get_object()

        # Materiais que utilizam este material padrão
        context['materiais'] = material_padrao.materiais.all()

        return context


class MaterialPadraoCreateView(CreateView):
    model = MaterialPadrao
    form_class = MaterialPadraoForm
    template_name = 'estoque/material_padrao_form.html'
    success_url = reverse_lazy('material-padrao-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material padrão cadastrado com sucesso!')
        return super().form_valid(form)


class MaterialPadraoUpdateView(UpdateView):
    model = MaterialPadrao
    form_class = MaterialPadraoForm
    template_name = 'estoque/material_padrao_form.html'
    success_url = reverse_lazy('material-padrao-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material padrão atualizado com sucesso!')
        return super().form_valid(form)


class MaterialPadraoDeleteView(DeleteView):
    model = MaterialPadrao
    template_name = 'estoque/material_padrao_confirm_delete.html'
    success_url = reverse_lazy('material-padrao-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material_padrao = self.get_object()

        # Verificar se o material padrão está sendo usado em materiais
        materiais = material_padrao.materiais.all()
        context['materiais'] = materiais
        context['tem_materiais'] = materiais.exists()
        context['total_materiais'] = materiais.count()

        return context

    def post(self, request, *args, **kwargs):
        material_padrao = self.get_object()
        materiais = material_padrao.materiais.all()

        # Se o material padrão está sendo usado em materiais
        if materiais.exists():
            messages.error(request, f'Não é possível excluir o material padrão "{material_padrao}" pois está sendo usado em {materiais.count()} materiais.')
            return redirect('material-padrao-list')

        messages.success(request, f'Material padrão "{material_padrao}" excluído com sucesso!')
        return super().post(request, *args, **kwargs)


# Views para Materiais
class MaterialListView(ListView):
    model = Material
    template_name = 'estoque/material_list.html'
    context_object_name = 'materiais'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        self.filterset = MaterialFilter(self.request.GET, queryset=queryset)

        # Obter os resultados filtrados
        materiais_filtrados = list(self.filterset.qs)

        # Ordenar materiais por nome e diâmetro numérico
        materiais_ordenados = ordenar_materiais(materiais_filtrados)

        return materiais_ordenados

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset

        # Ordenar materiais padrão por nome
        materiais_padrao = MaterialPadrao.objects.all()
        context['materiais_padrao'] = sorted(materiais_padrao, key=lambda mp: mp.nome)

        return context


class MaterialDetailView(DetailView):
    model = Material
    template_name = 'estoque/material_detail.html'
    context_object_name = 'material'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material = self.get_object()

        # Histórico de movimentações
        context['movimentacoes'] = material.movimentacoes.all().order_by('-data')[:10]

        # Molas que utilizam este material
        context['molas'] = material.molas.all()

        return context


class MaterialCreateView(CreateView):
    model = Material
    form_class = MaterialForm
    template_name = 'estoque/material_form.html'
    success_url = reverse_lazy('material-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material cadastrado com sucesso!')
        return super().form_valid(form)


class MaterialUpdateView(UpdateView):
    model = Material
    form_class = MaterialForm
    template_name = 'estoque/material_form.html'
    success_url = reverse_lazy('material-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material atualizado com sucesso!')
        return super().form_valid(form)


class MaterialDeleteView(DeleteView):
    model = Material
    template_name = 'estoque/material_confirm_delete.html'
    success_url = reverse_lazy('material-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material = self.get_object()

        # Verificar se o material está sendo usado em molas
        molas = material.molas.all()
        context['molas'] = molas
        context['tem_molas'] = molas.exists()
        context['total_molas'] = molas.count()

        return context

    def post(self, request, *args, **kwargs):
        material = self.get_object()
        molas = material.molas.all()

        # Se o material está sendo usado em molas
        if molas.exists():
            acao = request.POST.get('acao')

            # Inativar o material em vez de excluí-lo
            if acao == 'inativar':
                material.ativo = False
                material.save()
                messages.success(request, f'Material "{material}" foi inativado com sucesso!')
                return redirect('material-list')

            # Tentar forçar a exclusão (provavelmente falhará devido à proteção da chave estrangeira)
            elif acao == 'forcar':
                try:
                    return super().post(request, *args, **kwargs)
                except Exception as e:
                    messages.error(request, f'Não foi possível excluir o material: {str(e)}')
                    return redirect('material-detail', pk=material.pk)

            # Se nenhuma ação foi especificada, redirecionar de volta para a página de confirmação
            else:
                messages.warning(request, 'Selecione uma ação para continuar.')
                return redirect('material-delete', pk=material.pk)

        # Se o material não está sendo usado, prosseguir com a exclusão normal
        return super().post(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Material excluído com sucesso!')
        return super().delete(request, *args, **kwargs)


# Views para Movimentações de Estoque
class MovimentacaoEstoqueListView(ListView):
    model = MovimentacaoEstoque
    template_name = 'estoque/movimentacao_estoque_list.html'
    context_object_name = 'movimentacoes'
    paginate_by = 10

    def get_queryset(self):
        # Forçar consulta atualizada sem cache
        queryset = MovimentacaoEstoque.objects.all().select_related('mola')
        self.filterset = MovimentacaoEstoqueFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        # Adicionar timestamp para mostrar quando os dados foram atualizados
        context['timestamp'] = timezone.now()
        return context

    def dispatch(self, request, *args, **kwargs):
        # Desativar cache para esta view
        response = super().dispatch(request, *args, **kwargs)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response


class MovimentacaoEstoqueCreateView(CreateView):
    model = MovimentacaoEstoque
    form_class = MovimentacaoEstoqueForm
    template_name = 'estoque/movimentacao_estoque_form.html'
    success_url = reverse_lazy('movimentacao-estoque-list')

    def get_initial(self):
        """Inicializa o formulário com os parâmetros da URL"""
        initial = super().get_initial()

        # Verificar se há parâmetros na URL
        if 'mola' in self.request.GET:
            initial['mola'] = self.request.GET.get('mola')

        if 'tipo' in self.request.GET:
            initial['tipo'] = self.request.GET.get('tipo')

        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Movimentação registrada com sucesso!')

        # Limpar cache explicitamente
        cache.delete('dashboard_data')
        cache.delete('mola_list_queryset')

        return response


def movimentacao_multipla(request):
    """View para registrar entrada ou saída de múltiplas molas de um mesmo cliente"""
    if request.method == 'POST':
        form = MovimentacaoMultiplaForm(request.POST)
        if form.is_valid():
            cliente = form.cleaned_data['cliente']
            tipo = form.cleaned_data['tipo']
            ordem_venda = form.cleaned_data['ordem_venda']
            observacao = form.cleaned_data['observacao']

            # Processar os campos dinâmicos (molas e quantidades)
            molas_processadas = 0
            erros = []

            # Iterar sobre os pares de nome_mola e quantidade
            for key, value in request.POST.items():
                if key.startswith('nome_mola_') and value:
                    # Extrair o índice do campo
                    idx = key.split('_')[-1]
                    nome_mola = value
                    quantidade_key = f'quantidade_{idx}'

                    # Verificar se existe o campo de quantidade correspondente
                    if quantidade_key in request.POST and request.POST[quantidade_key]:
                        try:
                            quantidade = int(request.POST[quantidade_key])

                            # Buscar a mola pelo nome e cliente
                            mola = Mola.buscar_por_nome_e_cliente(nome_mola, cliente)

                            # Se não encontrar pelo nome_mola, tentar buscar pelo código
                            if not mola:
                                # Tentar encontrar molas que contenham o nome_mola no código
                                molas_possiveis = Mola.objects.filter(codigo__contains=nome_mola, cliente=cliente)
                                if molas_possiveis.count() == 1:
                                    mola = molas_possiveis.first()

                            if mola:
                                # Verificar estoque para saídas
                                if tipo == 'S' and quantidade > mola.quantidade_estoque:
                                    erros.append(f"Mola {mola.codigo}: Quantidade insuficiente em estoque. Disponível: {mola.quantidade_estoque}")
                                    continue

                                # Criar a movimentação
                                MovimentacaoEstoque.objects.create(
                                    mola=mola,
                                    tipo=tipo,
                                    quantidade=quantidade,
                                    ordem_venda=ordem_venda,
                                    observacao=observacao
                                )
                                molas_processadas += 1
                            else:
                                erros.append(f"Mola com nome '{nome_mola}' não encontrada para o cliente {cliente}")
                        except ValueError:
                            erros.append(f"Quantidade inválida para a mola {nome_mola}")

            # Exibir mensagens de sucesso e erro
            if molas_processadas > 0:
                messages.success(request, f'{molas_processadas} movimentações registradas com sucesso!')

                # Limpar cache explicitamente para garantir que as movimentações apareçam imediatamente
                cache.delete('dashboard_data')
                cache.delete('mola_list_queryset')

            for erro in erros:
                messages.error(request, erro)

            if molas_processadas > 0:
                return redirect('movimentacao-estoque-list')
    else:
        # Pré-selecionar o tipo se fornecido na URL
        initial = {}
        if 'tipo' in request.GET:
            initial['tipo'] = request.GET.get('tipo')

        form = MovimentacaoMultiplaForm(initial=initial)

    # Obter lista de clientes para o dropdown
    clientes = Mola.objects.values_list('cliente', flat=True).distinct()

    return render(request, 'estoque/movimentacao_multipla_form.html', {
        'form': form,
        'clientes': clientes
    })


# Views para Movimentações de Material
class MovimentacaoMaterialListView(ListView):
    model = MovimentacaoMaterial
    template_name = 'estoque/movimentacao_material_list.html'
    context_object_name = 'movimentacoes'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        self.filterset = MovimentacaoMaterialFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        return context


class MovimentacaoMaterialCreateView(CreateView):
    model = MovimentacaoMaterial
    form_class = MovimentacaoMaterialForm
    template_name = 'estoque/movimentacao_material_form.html'
    success_url = reverse_lazy('movimentacao-material-list')

    def get_initial(self):
        """Inicializa o formulário com os parâmetros da URL"""
        initial = super().get_initial()

        # Verificar se há parâmetros na URL
        if 'material' in self.request.GET:
            initial['material'] = self.request.GET.get('material')

        if 'tipo' in self.request.GET:
            initial['tipo'] = self.request.GET.get('tipo')

        return initial

    def form_valid(self, form):
        messages.success(self.request, 'Movimentação registrada com sucesso!')
        return super().form_valid(form)


# Views para Pedidos de Venda
class PedidoVendaListView(ListView):
    model = PedidoVenda
    template_name = 'estoque/pedido_venda_list.html'
    context_object_name = 'pedidos'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        self.filterset = PedidoVendaFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        return context


class PedidoVendaDetailView(DetailView):
    model = PedidoVenda
    template_name = 'estoque/pedido_venda_detail.html'
    context_object_name = 'pedido'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        pedido = self.get_object()

        # Itens do pedido
        context['itens'] = pedido.itens.all()

        return context


class PedidoVendaCreateView(CreateView):
    model = PedidoVenda
    form_class = PedidoVendaForm
    template_name = 'estoque/pedido_venda_form.html'
    success_url = reverse_lazy('pedido-venda-list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Pedido cadastrado com sucesso!')

        # Sempre redireciona para adicionar itens ao pedido
        return redirect('pedido-venda-add-item-multiplo', pk=self.object.pk)


class PedidoVendaUpdateView(UpdateView):
    model = PedidoVenda
    form_class = PedidoVendaForm
    template_name = 'estoque/pedido_venda_form.html'
    success_url = reverse_lazy('pedido-venda-list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Pedido atualizado com sucesso!')

        # Sempre redireciona para adicionar itens ao pedido
        return redirect('pedido-venda-add-item-multiplo', pk=self.object.pk)


def pedido_venda_add_item(request, pk):
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    # Redirecionar para a versão de adição múltipla
    return redirect('pedido-venda-add-item-multiplo', pk=pedido.pk)


def pedido_venda_add_item_multiplo(request, pk):
    """View para adicionar múltiplos itens ao pedido de uma vez"""
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    if request.method == 'POST':
        # Processar os campos dinâmicos (molas e quantidades)
        itens_processados = 0
        erros = []

        # Verificar se deve movimentar estoque para todos os itens
        movimentar_estoque_geral = request.POST.get('movimentar_estoque_geral') == 'on'

        # Se não deve movimentar estoque, adiciona observação ao pedido
        if not movimentar_estoque_geral:
            if pedido.observacao:
                if "Sem movimentação no estoque" not in pedido.observacao:
                    pedido.observacao += "\nSem movimentação no estoque"
                    pedido.save(update_fields=['observacao'])
            else:
                pedido.observacao = "Sem movimentação no estoque"
                pedido.save(update_fields=['observacao'])

        # Iterar sobre os pares de campos nome_mola_X e quantidade_X
        i = 1
        while f'nome_mola_{i}' in request.POST:
            nome_mola = request.POST.get(f'nome_mola_{i}')
            quantidade_str = request.POST.get(f'quantidade_{i}')

            if nome_mola and quantidade_str:
                try:
                    # Buscar a mola pelo código (nome_mola)
                    # Filtrar primeiro pelo cliente do pedido para evitar conflitos entre clientes
                    mola = None
                    cliente_pedido = pedido.cliente

                    # 1. Primeiro tentamos buscar pelo código exato do cliente específico
                    try:
                        mola = Mola.objects.get(codigo=nome_mola, cliente=cliente_pedido, ativo=True)
                    except Mola.DoesNotExist:
                        # 2. Se não encontrar, tentamos buscar pelo nome_mola (número após a barra) do cliente específico
                        try:
                            mola = Mola.objects.get(nome_mola=nome_mola, cliente=cliente_pedido, ativo=True)
                        except (Mola.DoesNotExist, Mola.MultipleObjectsReturned):
                            # 3. Se ainda não encontrar ou houver múltiplos, tentamos buscar por código que termine com o nome_mola do cliente específico
                            molas_encontradas = Mola.objects.filter(
                                codigo__endswith=nome_mola,
                                cliente=cliente_pedido,
                                ativo=True
                            ).order_by('codigo')

                            if molas_encontradas.count() == 1:
                                mola = molas_encontradas.first()
                            elif molas_encontradas.count() > 1:
                                # Se houver múltiplas molas do mesmo cliente, listar as opções
                                codigos = [m.codigo for m in molas_encontradas[:5]]  # Limitar a 5 para não poluir
                                erros.append(f"Múltiplas molas encontradas para '{nome_mola}' do cliente {cliente_pedido}: {', '.join(codigos)}. Use o código completo.")
                                i += 1
                                continue
                    except Mola.MultipleObjectsReturned:
                        # Se houver múltiplas molas com o mesmo código do mesmo cliente (não deveria acontecer devido ao unique=True)
                        erros.append(f"Erro interno: múltiplas molas com código '{nome_mola}' para o cliente {cliente_pedido}. Contate o administrador.")
                        i += 1
                        continue

                    if not mola:
                        erros.append(f"Mola '{nome_mola}' não encontrada para o cliente {cliente_pedido}.")
                        i += 1
                        continue

                    quantidade = int(quantidade_str)

                    # Usar a configuração geral de movimentação de estoque
                    movimentar_estoque = movimentar_estoque_geral

                    if quantidade <= 0:
                        erros.append(f"Mola {mola.codigo}: A quantidade deve ser maior que zero.")
                    elif movimentar_estoque and quantidade > mola.quantidade_estoque:
                        erros.append(f"Mola {mola.codigo}: Quantidade insuficiente em estoque. Disponível: {mola.quantidade_estoque}")
                    else:
                        # Criar o item do pedido
                        item = ItemPedido(
                            pedido=pedido,
                            mola=mola,
                            quantidade=quantidade,
                            movimentar_estoque=movimentar_estoque
                        )
                        item.save()

                        # Se o pedido estiver aprovado, processa a saída do estoque
                        if pedido.status == 'A':
                            item.processar_saida()

                        itens_processados += 1
                except ValueError:
                    erros.append(f"Quantidade inválida para a mola {nome_mola}.")

            i += 1

        # Exibir mensagens de sucesso e erro
        if itens_processados > 0:
            messages.success(request, f'{itens_processados} itens adicionados com sucesso!')

        for erro in erros:
            messages.error(request, erro)

        if itens_processados > 0:
            return redirect('pedido-venda-detail', pk=pedido.pk)

    return render(request, 'estoque/item_pedido_multiplo_form.html', {
        'pedido': pedido
    })


def pedido_venda_processar(request, pk):
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    if pedido.status != 'A':
        pedido.status = 'A'
        pedido.save()

        # Processa todos os itens do pedido
        itens_processados = 0
        for item in pedido.itens.filter(atendido=False):
            if item.processar_saida():
                itens_processados += 1

        if itens_processados > 0:
            messages.success(request, f'{itens_processados} itens processados com sucesso!')
        else:
            messages.warning(request, 'Nenhum item foi processado. Verifique o estoque.')
    else:
        messages.info(request, 'Este pedido já foi processado.')

    return redirect('pedido-venda-detail', pk=pedido.pk)


def pedido_venda_cancelar(request, pk):
    """View para cancelar um pedido de venda"""
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    if request.method == 'POST':
        # Atualiza o status do pedido para cancelado
        pedido.status = 'C'
        pedido.save()

        messages.success(request, f'Pedido {pedido.numero_pedido} cancelado com sucesso!')
        return redirect('pedido-venda-list')

    # Renderiza a página de confirmação
    return render(request, 'estoque/pedido_venda_cancelar.html', {
        'pedido': pedido
    })


class PedidoVendaDeleteView(DeleteView):
    """View para excluir um pedido de venda"""
    model = PedidoVenda
    template_name = 'estoque/pedido_venda_confirm_delete.html'
    success_url = reverse_lazy('pedido-venda-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Garantir que o objeto pedido esteja disponível no contexto
        context['pedido'] = self.get_object()
        return context

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Pedido excluído com sucesso!')
        return super().delete(request, *args, **kwargs)


class ItemPedidoDeleteView(DeleteView):
    """View para excluir um item de pedido"""
    model = ItemPedido
    template_name = 'estoque/item_pedido_confirm_delete.html'

    def get_success_url(self):
        return reverse_lazy('pedido-venda-detail', kwargs={'pk': self.object.pedido.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item'] = self.get_object()
        context['pedido'] = self.object.pedido
        return context

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Item removido do pedido com sucesso!')
        return super().delete(request, *args, **kwargs)







# Views para Relatórios
def materiais_json(request):
    """View para retornar todos os materiais em formato JSON"""
    # Filtrar apenas materiais ativos
    materiais = Material.objects.filter(ativo=True)

    # Aplicar filtros se fornecidos
    material_padrao_id = request.GET.get('material_padrao')
    if material_padrao_id:
        try:
            materiais = materiais.filter(material_padrao_id=material_padrao_id)
        except (ValueError, TypeError):
            pass

    # Formatar para JSON
    materiais_json = []
    for material in materiais:
        materiais_json.append({
            'id': material.id,
            'nome': material.nome,
            'diametro': material.diametro,
            'material_padrao_id': material.material_padrao_id if material.material_padrao else None,
            'material_padrao_nome': str(material.material_padrao) if material.material_padrao else None,
            'quantidade_estoque': float(material.quantidade_estoque)
        })

    # Ordenar materiais por nome e diâmetro numérico
    materiais_json = ordenar_materiais(materiais_json)

    return JsonResponse(materiais_json, safe=False)


def relatorio_molas_mais_vendidas(request):
    if request.method == 'POST':
        form = RelatorioMolasForm(request.POST)
        if form.is_valid():
            periodo = form.cleaned_data.get('periodo')
            data_inicial = form.cleaned_data.get('data_inicial')
            data_final = form.cleaned_data.get('data_final')
            subtitulo_personalizado = form.cleaned_data.get('subtitulo_personalizado')
            cliente = form.cleaned_data.get('cliente')
            limite = form.cleaned_data.get('limite') or 10

            # Formato de saída
            formato = request.POST.get('formato', 'html')

            # Verificar se é comparação entre períodos
            if periodo == 'comparacao':
                return processar_comparacao_periodos(request, form, formato)

            # Verificar se é o caso de meses específicos
            if periodo == 'meses_especificos':
                meses_selecionados = form.cleaned_data.get('meses_selecionados')
                ano_selecionado = form.cleaned_data.get('ano_selecionado')

                if not meses_selecionados:
                    messages.error(request, 'Selecione pelo menos um mês para gerar o relatório.')
                    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {'form': form})

                # Gerar múltiplos relatórios, um para cada mês selecionado
                if formato == 'pdf':
                    # Criar um arquivo PDF para cada mês selecionado
                    import io
                    from PyPDF2 import PdfMerger

                    # Criar um merger para juntar todos os PDFs
                    merger = PdfMerger()

                    for mes in meses_selecionados:
                        # Converter mês e ano para datas de início e fim
                        mes_int = int(mes)
                        ano_int = int(ano_selecionado)

                        # Primeiro dia do mês
                        primeiro_dia = date(ano_int, mes_int, 1)

                        # Último dia do mês (calculando o primeiro dia do próximo mês e subtraindo 1 dia)
                        if mes_int == 12:
                            ultimo_dia = date(ano_int + 1, 1, 1) - timedelta(days=1)
                        else:
                            ultimo_dia = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

                        # Obter nome do mês para o título
                        nome_mes = dict(form.MESES_CHOICES).get(mes)
                        mes_periodo_texto = f"{nome_mes} de {ano_selecionado}"

                        # Obter dados para este mês específico
                        resultado_mes = Mola.mais_vendidas(
                            periodo='personalizado',
                            data_inicial=primeiro_dia,
                            data_final=ultimo_dia
                        )

                        # Filtra por cliente se especificado
                        if cliente:
                            resultado_mes = [item for item in resultado_mes if cliente.lower() in item['mola_cliente'].lower()]

                        # Limita o número de resultados
                        resultado_mes = resultado_mes[:limite]

                        # Gerar PDF para este mês
                        pdf_buffer = io.BytesIO()
                        gerar_pdf_molas_mais_vendidas(
                            resultado_mes,
                            'personalizado',
                            mes_periodo_texto,
                            primeiro_dia,
                            ultimo_dia,
                            output_buffer=pdf_buffer
                        )

                        # Adicionar ao merger
                        pdf_buffer.seek(0)
                        merger.append(pdf_buffer)

                    # Criar resposta HTTP para o PDF combinado
                    response = HttpResponse(content_type='application/pdf')
                    response['Content-Disposition'] = f'attachment; filename="molas_mais_vendidas_meses_especificos_{ano_selecionado}.pdf"'

                    # Escrever o PDF combinado na resposta
                    merger.write(response)
                    merger.close()

                    return response

                elif formato == 'csv':
                    # Criar um arquivo CSV para cada mês selecionado
                    import io
                    import zipfile

                    # Criar um buffer para o arquivo ZIP
                    zip_buffer = io.BytesIO()

                    # Criar um arquivo ZIP
                    with zipfile.ZipFile(zip_buffer, 'a', zipfile.ZIP_DEFLATED, False) as zip_file:
                        for mes in meses_selecionados:
                            # Converter mês e ano para datas de início e fim
                            mes_int = int(mes)
                            ano_int = int(ano_selecionado)

                            # Primeiro dia do mês
                            primeiro_dia = date(ano_int, mes_int, 1)

                            # Último dia do mês
                            if mes_int == 12:
                                ultimo_dia = date(ano_int + 1, 1, 1) - timedelta(days=1)
                            else:
                                ultimo_dia = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

                            # Obter nome do mês para o título
                            nome_mes = dict(form.MESES_CHOICES).get(mes)
                            mes_periodo_texto = f"{nome_mes} de {ano_selecionado}"

                            # Obter dados para este mês específico
                            resultado_mes = Mola.mais_vendidas(
                                periodo='personalizado',
                                data_inicial=primeiro_dia,
                                data_final=ultimo_dia
                            )

                            # Filtra por cliente se especificado
                            if cliente:
                                resultado_mes = [item for item in resultado_mes if cliente.lower() in item['mola_cliente'].lower()]

                            # Limita o número de resultados
                            resultado_mes = resultado_mes[:limite]

                            # Gerar CSV para este mês
                            csv_buffer = io.StringIO()
                            writer = csv.writer(csv_buffer)

                            # Cabeçalho com informação do período
                            writer.writerow(['Relatório de Molas Mais Vendidas'])
                            writer.writerow([f'Período: {mes_periodo_texto}'])
                            writer.writerow([])  # Linha em branco

                            # Cabeçalho dos dados
                            writer.writerow(['Pos.', 'Código', 'Cliente', 'Qtd. Vendida', 'Média Mensal', 'Variação %'])

                            # Dados
                            for i, item in enumerate(resultado_mes, 1):
                                writer.writerow([
                                    i,
                                    item['mola_codigo'],
                                    item['mola_cliente'],
                                    item['total_vendido'],
                                    item.get('media_mensal', 0),
                                    f"{item.get('variacao_percentual', 0)}%"
                                ])

                            # Adicionar o CSV ao arquivo ZIP
                            nome_arquivo = f"molas_mais_vendidas_{nome_mes.lower()}_{ano_selecionado}.csv"
                            zip_file.writestr(nome_arquivo, csv_buffer.getvalue())

                    # Criar resposta HTTP para o arquivo ZIP
                    response = HttpResponse(content_type='application/zip')
                    response['Content-Disposition'] = f'attachment; filename="molas_mais_vendidas_meses_especificos_{ano_selecionado}.zip"'

                    # Escrever o arquivo ZIP na resposta
                    zip_buffer.seek(0)
                    response.write(zip_buffer.getvalue())

                    return response

                else:
                    # Para visualização na tela, mostrar apenas o primeiro mês selecionado
                    mes = meses_selecionados[0]
                    mes_int = int(mes)
                    ano_int = int(ano_selecionado)

                    # Primeiro dia do mês
                    primeiro_dia = date(ano_int, mes_int, 1)

                    # Último dia do mês
                    if mes_int == 12:
                        ultimo_dia = date(ano_int + 1, 1, 1) - timedelta(days=1)
                    else:
                        ultimo_dia = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

                    # Obter nome do mês para o título
                    nome_mes = dict(form.MESES_CHOICES).get(mes)
                    mes_periodo_texto = f"{nome_mes} de {ano_selecionado}"

                    # Obter dados para este mês específico
                    resultado = Mola.mais_vendidas(
                        periodo='personalizado',
                        data_inicial=primeiro_dia,
                        data_final=ultimo_dia
                    )

                    # Filtra por cliente se especificado
                    if cliente:
                        resultado = [item for item in resultado if cliente.lower() in item['mola_cliente'].lower()]

                    # Limita o número de resultados
                    resultado = resultado[:limite]

                    # Calcular variação percentual das vendas totais do mês
                    variacao_vendas_totais = Mola.calcular_variacao_vendas_totais_mes(primeiro_dia, ultimo_dia)

                    # Exibe na tela
                    messages.info(request, f'Exibindo dados para {mes_periodo_texto}. Para ver todos os meses selecionados, use os botões de exportação.')
                    if variacao_vendas_totais != 0:
                        if variacao_vendas_totais > 0:
                            messages.success(request, f'Vendas totais do mês: +{variacao_vendas_totais}% em relação à média histórica')
                        else:
                            messages.warning(request, f'Vendas totais do mês: {variacao_vendas_totais}% em relação à média histórica')

                    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {
                        'form': form,
                        'resultado': resultado,
                        'periodo': periodo,
                        'periodo_texto': mes_periodo_texto,
                        'data_inicial': primeiro_dia,
                        'data_final': ultimo_dia,
                        'variacao_vendas_totais': variacao_vendas_totais
                    })

            else:
                # Processamento normal para outros períodos
                # Obtém as molas mais vendidas
                resultado = Mola.mais_vendidas(
                    periodo=periodo,
                    data_inicial=data_inicial,
                    data_final=data_final
                )

                # Filtra por cliente se especificado
                if cliente:
                    resultado = [item for item in resultado if cliente.lower() in item['mola_cliente'].lower()]

                # Limita o número de resultados
                resultado = resultado[:limite]

                # Define o texto do período para exibição
                if subtitulo_personalizado:
                    # Usa o subtítulo personalizado fornecido pelo usuário
                    periodo_texto = subtitulo_personalizado
                elif periodo == 'personalizado' and data_inicial and data_final:
                    periodo_texto = f"De {data_inicial.strftime('%d/%m/%Y')} a {data_final.strftime('%d/%m/%Y')}"
                else:
                    periodo_texto = dict(form.fields['periodo'].choices).get(periodo, 'Todo o período')

                if formato == 'pdf':
                    # Gera PDF
                    return gerar_pdf_molas_mais_vendidas(resultado, periodo, periodo_texto, data_inicial, data_final)
                elif formato == 'csv':
                    # Gera CSV
                    return gerar_csv_molas_mais_vendidas(resultado, periodo, periodo_texto)
                else:
                    # Exibe na tela
                    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {
                        'form': form,
                        'resultado': resultado,
                        'periodo': periodo,
                        'periodo_texto': periodo_texto,
                        'data_inicial': data_inicial,
                        'data_final': data_final
                    })
    else:
        form = RelatorioMolasForm()

    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {'form': form})


def processar_comparacao_periodos(request, form, formato):
    """Processa a comparação entre dois períodos específicos"""
    # Obter dados dos períodos
    data_inicial_comp1 = form.cleaned_data.get('data_inicial_comparacao1')
    data_final_comp1 = form.cleaned_data.get('data_final_comparacao1')
    data_inicial_comp2 = form.cleaned_data.get('data_inicial_comparacao2')
    data_final_comp2 = form.cleaned_data.get('data_final_comparacao2')

    # Verificar se foram usados seletores de mês/ano
    mes_comp1 = form.cleaned_data.get('mes_comparacao1')
    ano_comp1 = form.cleaned_data.get('ano_comparacao1')
    mes_comp2 = form.cleaned_data.get('mes_comparacao2')
    ano_comp2 = form.cleaned_data.get('ano_comparacao2')

    # Se os seletores de mês/ano foram preenchidos, usar eles ao invés das datas manuais
    if mes_comp1 and ano_comp1:
        mes_int = int(mes_comp1)
        ano_int = int(ano_comp1)
        data_inicial_comp1 = date(ano_int, mes_int, 1)
        # Último dia do mês
        if mes_int == 12:
            data_final_comp1 = date(ano_int + 1, 1, 1) - timedelta(days=1)
        else:
            data_final_comp1 = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

    if mes_comp2 and ano_comp2:
        mes_int = int(mes_comp2)
        ano_int = int(ano_comp2)
        data_inicial_comp2 = date(ano_int, mes_int, 1)
        # Último dia do mês
        if mes_int == 12:
            data_final_comp2 = date(ano_int + 1, 1, 1) - timedelta(days=1)
        else:
            data_final_comp2 = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

    subtitulo_personalizado_comparacao = form.cleaned_data.get('subtitulo_personalizado_comparacao')
    cliente = form.cleaned_data.get('cliente')
    limite = form.cleaned_data.get('limite') or 10

    # Obter vendas para o período 1
    resultado_periodo1 = Mola.mais_vendidas_periodo_personalizado(
        data_inicial_comp1, data_final_comp1
    )

    # Obter vendas para o período 2
    resultado_periodo2 = Mola.mais_vendidas_periodo_personalizado(
        data_inicial_comp2, data_final_comp2
    )

    # Filtrar por cliente se especificado
    if cliente:
        resultado_periodo1 = [item for item in resultado_periodo1 if cliente.lower() in item['mola_cliente'].lower()]
        resultado_periodo2 = [item for item in resultado_periodo2 if cliente.lower() in item['mola_cliente'].lower()]

    # Limitar resultados
    resultado_periodo1 = resultado_periodo1[:limite]
    resultado_periodo2 = resultado_periodo2[:limite]

    # Criar dicionários para facilitar comparação
    vendas_periodo1 = {item['mola_codigo']: item['total_vendido'] for item in resultado_periodo1}
    vendas_periodo2 = {item['mola_codigo']: item['total_vendido'] for item in resultado_periodo2}

    # Combinar todas as molas dos dois períodos
    todas_molas = set(vendas_periodo1.keys()) | set(vendas_periodo2.keys())

    # Determinar qual período é mais recente para calcular variação corretamente
    periodo1_mais_recente = data_inicial_comp1 > data_inicial_comp2

    # Criar resultado comparativo
    resultado_comparacao = []
    for codigo_mola in todas_molas:
        vendas_p1 = vendas_periodo1.get(codigo_mola, 0)
        vendas_p2 = vendas_periodo2.get(codigo_mola, 0)

        # Calcular variação percentual: (valor_recente - valor_antigo) / valor_antigo * 100
        if periodo1_mais_recente:
            # Período 1 é mais recente
            if vendas_p2 == 0:
                variacao = 100 if vendas_p1 > 0 else 0
            else:
                variacao = ((vendas_p1 - vendas_p2) / vendas_p2) * 100
        else:
            # Período 2 é mais recente
            if vendas_p1 == 0:
                variacao = 100 if vendas_p2 > 0 else 0
            else:
                variacao = ((vendas_p2 - vendas_p1) / vendas_p1) * 100

        # Encontrar informações da mola
        mola_info = None
        for item in resultado_periodo1 + resultado_periodo2:
            if item['mola_codigo'] == codigo_mola:
                mola_info = item
                break

        if mola_info:
            # Calcular diferença correta: período mais recente - período mais antigo
            if periodo1_mais_recente:
                diferenca_absoluta = vendas_p1 - vendas_p2  # P1 é mais recente
            else:
                diferenca_absoluta = vendas_p2 - vendas_p1  # P2 é mais recente

            resultado_comparacao.append({
                'mola_codigo': codigo_mola,
                'mola_cliente': mola_info['mola_cliente'],
                'vendas_periodo1': vendas_p1,
                'vendas_periodo2': vendas_p2,
                'variacao_percentual': round(variacao, 1),
                'diferenca_absoluta': diferenca_absoluta,
                'total_vendas_ambos_periodos': vendas_p1 + vendas_p2  # Para ordenação
            })

    # Ordenar por vendas totais dos dois períodos (seguindo a mesma lógica do relatório "Molas Mais Vendidas")
    resultado_comparacao.sort(key=lambda x: x['total_vendas_ambos_periodos'], reverse=True)

    # Preparar textos dos períodos
    # Se foi usado seletor de mês/ano, mostrar formato mais amigável
    if mes_comp1 and ano_comp1:
        meses_nomes = {
            '1': 'Janeiro', '2': 'Fevereiro', '3': 'Março', '4': 'Abril',
            '5': 'Maio', '6': 'Junho', '7': 'Julho', '8': 'Agosto',
            '9': 'Setembro', '10': 'Outubro', '11': 'Novembro', '12': 'Dezembro'
        }
        periodo1_texto = f"{meses_nomes[mes_comp1]} de {ano_comp1}"
    else:
        periodo1_texto = f"De {data_inicial_comp1.strftime('%d/%m/%Y')} a {data_final_comp1.strftime('%d/%m/%Y')}"

    if mes_comp2 and ano_comp2:
        meses_nomes = {
            '1': 'Janeiro', '2': 'Fevereiro', '3': 'Março', '4': 'Abril',
            '5': 'Maio', '6': 'Junho', '7': 'Julho', '8': 'Agosto',
            '9': 'Setembro', '10': 'Outubro', '11': 'Novembro', '12': 'Dezembro'
        }
        periodo2_texto = f"{meses_nomes[mes_comp2]} de {ano_comp2}"
    else:
        periodo2_texto = f"De {data_inicial_comp2.strftime('%d/%m/%Y')} a {data_final_comp2.strftime('%d/%m/%Y')}"

    if formato == 'pdf':
        return gerar_pdf_comparacao_periodos(
            resultado_comparacao, periodo1_texto, periodo2_texto,
            data_inicial_comp1, data_final_comp1, data_inicial_comp2, data_final_comp2,
            subtitulo_personalizado_comparacao
        )
    elif formato == 'csv':
        return gerar_csv_comparacao_periodos(
            resultado_comparacao, periodo1_texto, periodo2_texto
        )
    else:
        return render(request, 'estoque/relatorio_comparacao_periodos.html', {
            'form': form,
            'resultado_comparacao': resultado_comparacao,
            'periodo1_texto': periodo1_texto,
            'periodo2_texto': periodo2_texto,
            'subtitulo_personalizado_comparacao': subtitulo_personalizado_comparacao,
            'data_inicial_comp1': data_inicial_comp1,
            'data_final_comp1': data_final_comp1,
            'data_inicial_comp2': data_inicial_comp2,
            'data_final_comp2': data_final_comp2
        })


def gerar_pdf_comparacao_periodos(resultado_comparacao, periodo1_texto, periodo2_texto,
                                 data_inicial_comp1, data_final_comp1, data_inicial_comp2, data_final_comp2,
                                 subtitulo_personalizado=None):
    """Gera PDF da comparação entre períodos"""
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="comparacao_periodos.pdf"'

    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    styles = get_styles()

    # Adiciona descrição
    elements.append(Spacer(1, 0.5*cm))

    # Adicionar subtítulo personalizado se fornecido
    if subtitulo_personalizado:
        elements.append(Paragraph(subtitulo_personalizado, styles['subtitle']))
        elements.append(Spacer(1, 0.3*cm))

    elements.append(Paragraph(
        f"Comparação de vendas entre dois períodos específicos:",
        styles['normal']
    ))
    elements.append(Paragraph(
        f"<b>Período 1:</b> {periodo1_texto}",
        styles['normal']
    ))
    elements.append(Paragraph(
        f"<b>Período 2:</b> {periodo2_texto}",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Dados da tabela
    data = [['Código', 'Cliente', 'Período 1', 'Período 2', 'Variação %', 'Diferença']]

    for item in resultado_comparacao:
        variacao_str = f"{item['variacao_percentual']:+.1f}%"
        diferenca_str = f"{item['diferenca_absoluta']:+d}"

        data.append([
            item['mola_codigo'],
            item['mola_cliente'],
            str(item['vendas_periodo1']),
            str(item['vendas_periodo2']),
            variacao_str,
            diferenca_str
        ])

    # Cria a tabela com larguras ajustadas
    # Código: 2.8cm, Cliente: 4.5cm, Período 1: 2.2cm, Período 2: 2.2cm, Variação %: 2.5cm, Diferença: 2.2cm
    table = Table(data, colWidths=[2.8*cm, 4.5*cm, 2.2*cm, 2.2*cm, 2.5*cm, 2.2*cm])
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    # Adiciona formatação condicional para variação
    for i in range(1, len(data)):
        variacao = resultado_comparacao[i-1]['variacao_percentual']
        if variacao > 0:
            table.setStyle(TableStyle([('TEXTCOLOR', (4, i), (4, i), colors.green)]))
        elif variacao < 0:
            table.setStyle(TableStyle([('TEXTCOLOR', (4, i), (4, i), colors.red)]))

    elements.append(table)
    elements.append(Spacer(1, 0.5*cm))

    # Resumo
    total_periodo1 = sum(item['vendas_periodo1'] for item in resultado_comparacao)
    total_periodo2 = sum(item['vendas_periodo2'] for item in resultado_comparacao)
    variacao_total = ((total_periodo1 - total_periodo2) / total_periodo2 * 100) if total_periodo2 > 0 else 0

    elements.append(Paragraph("Resumo da Comparação", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    resumo_data = [
        ["Total Vendido - Período 1", str(total_periodo1)],
        ["Total Vendido - Período 2", str(total_periodo2)],
        ["Variação Total", f"{variacao_total:+.1f}%"],
        ["Diferença Absoluta", f"{total_periodo1 - total_periodo2:+d}"]
    ]

    resumo_table = Table(resumo_data, colWidths=[8*cm, 8*cm])
    resumo_table.setStyle(create_table_style(has_header=False))
    elements.append(resumo_table)

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Comparação entre Períodos - Molas Mais Vendidas"
        filtros = f"Período 1: {periodo1_texto} | Período 2: {periodo2_texto}"
        add_header_footer(canvas, doc, titulo, filtros)

    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)
    return response


def gerar_csv_comparacao_periodos(resultado_comparacao, periodo1_texto, periodo2_texto):
    """Gera CSV da comparação entre períodos"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="comparacao_periodos.csv"'

    writer = csv.writer(response)

    # Cabeçalho com informação dos períodos
    writer.writerow(['Comparação entre Períodos - Molas Mais Vendidas'])
    writer.writerow([f'Período 1: {periodo1_texto}'])
    writer.writerow([f'Período 2: {periodo2_texto}'])
    writer.writerow([])  # Linha em branco

    # Cabeçalho dos dados
    writer.writerow(['Código', 'Cliente', 'Período 1', 'Período 2', 'Variação %', 'Diferença'])

    # Dados
    for item in resultado_comparacao:
        writer.writerow([
            item['mola_codigo'],
            item['mola_cliente'],
            item['vendas_periodo1'],
            item['vendas_periodo2'],
            f"{item['variacao_percentual']:+.1f}%",
            f"{item['diferenca_absoluta']:+d}"
        ])

    return response


def gerar_pdf_molas_mais_vendidas(resultado, periodo, periodo_texto=None, data_inicial=None, data_final=None, output_buffer=None):
    """Gera PDF do relatório de molas mais vendidas com design avançado."""
    # Se não for fornecido um buffer de saída, cria uma resposta HTTP
    if output_buffer is None:
        # Cria a resposta HTTP com o tipo de conteúdo PDF
        response = HttpResponse(content_type='application/pdf')

        # Define o nome do arquivo com base no período
        if periodo == 'personalizado' and data_inicial and data_final:
            filename = f"molas_mais_vendidas_{data_inicial.strftime('%Y%m%d')}_a_{data_final.strftime('%Y%m%d')}.pdf"
        else:
            filename = f"molas_mais_vendidas_{periodo or 'todos'}.pdf"

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # Usa a resposta como buffer de saída
        output = response
    else:
        # Usa o buffer fornecido
        output = output_buffer

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    doc = SimpleDocTemplate(output, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Se não foi fornecido um texto de período, gera um com base no período
    if not periodo_texto:
        periodo_texto = {
            'semana': 'Última Semana',
            'mes': 'Último Mês',
            '3meses': 'Últimos 3 Meses',
            '6meses': 'Últimos 6 Meses',
            'ano': 'Último Ano',
            'personalizado': f"De {data_inicial.strftime('%d/%m/%Y')} a {data_final.strftime('%d/%m/%Y')}",
            None: 'Todo o Período'
        }.get(periodo, 'Todo o Período')

    # Adiciona informações do relatório
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(f"Período: {periodo_texto}", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Adiciona descrição
    elements.append(Paragraph(
        "Este relatório apresenta as molas mais vendidas no período selecionado, "
        "ordenadas por quantidade vendida em ordem decrescente. Inclui média mensal de vendas "
        "e variação percentual em relação ao período anterior.",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # SEÇÃO DE RESUMO - Nova funcionalidade
    if resultado:
        # Calcular estatísticas do resumo
        total_vendas_periodo = sum(item['total_vendido'] for item in resultado)
        total_media_historica = sum(item.get('variacao_media_historica', 0) for item in resultado if item.get('variacao_media_historica') is not None)
        num_molas_com_dados = len([item for item in resultado if item.get('variacao_media_historica') is not None])

        # Calcular variação de vendas totais se temos data_inicial e data_final
        variacao_vendas_totais = 0
        if data_inicial and data_final:
            try:
                variacao_vendas_totais = Mola.calcular_variacao_vendas_totais_mes(data_inicial, data_final)
            except:
                variacao_vendas_totais = 0

        # Análise de tendência
        molas_acima_media = len([item for item in resultado if item.get('variacao_media_historica', 0) > 0])
        molas_abaixo_media = len([item for item in resultado if item.get('variacao_media_historica', 0) < 0])

        # Adicionar seção de resumo
        elements.append(Paragraph("Resumo da Análise", styles['subtitle']))
        elements.append(Spacer(1, 0.3*cm))

        resumo_data = []
        resumo_data.append(["Total de vendas no período", str(total_vendas_periodo)])

        if variacao_vendas_totais != 0:
            if variacao_vendas_totais > 0:
                resumo_data.append(["Variação vs. média histórica", f"+{variacao_vendas_totais}% (ACIMA da média)"])
            else:
                resumo_data.append(["Variação vs. média histórica", f"{variacao_vendas_totais}% (ABAIXO da média)"])

        if num_molas_com_dados > 0:
            resumo_data.append(["Molas acima da média histórica", f"{molas_acima_media} de {num_molas_com_dados}"])
            resumo_data.append(["Molas abaixo da média histórica", f"{molas_abaixo_media} de {num_molas_com_dados}"])

        # Análise de tendência
        if molas_acima_media > molas_abaixo_media:
            tendencia = "ALTA - Maioria das molas vendeu acima da média histórica"
        elif molas_abaixo_media > molas_acima_media:
            tendencia = "BAIXA - Maioria das molas vendeu abaixo da média histórica"
        else:
            tendencia = "ESTÁVEL - Vendas equilibradas em relação à média histórica"

        resumo_data.append(["Tendência geral", tendencia])

        # Criar tabela de resumo
        resumo_table = Table(resumo_data, colWidths=[8*cm, 8*cm])
        resumo_table.setStyle(create_table_style(has_header=False))
        elements.append(resumo_table)
        elements.append(Spacer(1, 0.5*cm))

    # Dados da tabela com novas colunas
    data = [['Pos.', 'Código', 'Cliente', 'Qtd. Vendida', 'Média Mensal', 'Variação %', 'Vs. Média Histórica']]

    for i, item in enumerate(resultado, 1):
        # Formatar a variação percentual com sinal e cor
        variacao = item.get('variacao_percentual', 0)
        if variacao > 0:
            variacao_str = f"+{variacao}%"
        elif variacao < 0:
            variacao_str = f"{variacao}%"
        else:
            variacao_str = "0%"

        # Formatar a variação em relação à média histórica
        variacao_historica = item.get('variacao_media_historica', 0)
        if variacao_historica > 0:
            variacao_historica_str = f"+{variacao_historica}%"
        elif variacao_historica < 0:
            variacao_historica_str = f"{variacao_historica}%"
        else:
            variacao_historica_str = "0%"

        data.append([
            str(i),
            item['mola_codigo'],
            item['mola_cliente'],
            str(item['total_vendido']),
            str(item.get('media_mensal', 0)),
            variacao_str,
            variacao_historica_str
        ])

    # Cria a tabela com larguras ajustadas para melhor visualização
    # Pos.: 1cm, Código: 2.8cm, Cliente: 4cm, Qtd. Vendida: 2.2cm,
    # Média Mensal: 2.2cm, Variação %: 2.2cm, Vs. Média Histórica: 2.8cm
    table = Table(data, colWidths=[1*cm, 2.8*cm, 4*cm, 2.2*cm, 2.2*cm, 2.2*cm, 2.8*cm])

    # Aplica estilo avançado à tabela
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    # Adiciona alinhamento específico para algumas colunas
    table_style = TableStyle([
        ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # Centraliza a coluna de posição
        ('ALIGN', (3, 1), (6, -1), 'RIGHT'),   # Alinha à direita as colunas numéricas
    ])

    # Adiciona cores para a variação percentual
    for i, item in enumerate(resultado, 1):
        variacao = item.get('variacao_percentual', 0)
        if variacao > 0:
            # Verde para variação positiva
            table_style.add('TEXTCOLOR', (5, i), (5, i), colors.green)
        elif variacao < 0:
            # Vermelho para variação negativa
            table_style.add('TEXTCOLOR', (5, i), (5, i), colors.red)

        # Adiciona cores para a variação em relação à média histórica
        variacao_historica = item.get('variacao_media_historica', 0)
        if variacao_historica > 0:
            # Verde para variação positiva
            table_style.add('TEXTCOLOR', (6, i), (6, i), colors.green)
        elif variacao_historica < 0:
            # Vermelho para variação negativa
            table_style.add('TEXTCOLOR', (6, i), (6, i), colors.red)

    table.setStyle(table_style)

    elements.append(table)
    elements.append(Spacer(1, 0.5*cm))

    # Adiciona resumo
    elements.append(Paragraph(f"Total de itens: {len(resultado)}", styles['info']))

    # Adiciona gráfico de tendência de vendas mensais
    # Usar KeepTogether para garantir que título e gráfico permaneçam juntos
    trend_elements = []
    trend_elements.append(Spacer(1, 1*cm))
    trend_elements.append(Paragraph("Tendência de Vendas Totais por Mês", styles['subtitle']))
    trend_elements.append(Spacer(1, 0.5*cm))

    # Obter dados de vendas mensais para o gráfico de tendência
    vendas_mensais = Mola.obter_vendas_mensais(periodo, data_inicial, data_final)

    if vendas_mensais['labels'] and vendas_mensais['valores']:
        # Criar gráfico de linha para tendência de vendas
        trend_chart = create_line_chart(
            vendas_mensais['valores'],
            vendas_mensais['labels'],
            "Tendência de Vendas Mensais",
            xlabel="Mês/Ano",
            ylabel="Quantidade Vendida"
        )
        trend_elements.append(trend_chart)
    else:
        trend_elements.append(Paragraph("Dados insuficientes para gerar o gráfico de tendência.", styles['info']))

    # Adicionar como um bloco que não pode ser quebrado
    elements.append(KeepTogether(trend_elements))

    # Se houver dados suficientes, adiciona um gráfico de barras das molas mais vendidas
    if len(resultado) > 1:
        # Usar KeepTogether para garantir que título e gráfico permaneçam juntos
        chart_elements = []
        chart_elements.append(Spacer(1, 1*cm))
        chart_elements.append(Paragraph("Gráfico de Vendas por Mola", styles['subtitle']))
        chart_elements.append(Spacer(1, 0.5*cm))

        # Prepara dados para o gráfico
        labels = [item['mola_codigo'] for item in resultado[:10]]  # Limita a 10 itens para o gráfico
        dados = [item['total_vendido'] for item in resultado[:10]]

        # Cria o gráfico
        chart = create_chart(
            dados,
            labels,
            "Top Molas - Quantidade Vendida",
            xlabel="Código da Mola",
            ylabel="Quantidade"
        )
        chart_elements.append(chart)

        # Adicionar como um bloco que não pode ser quebrado
        elements.append(KeepTogether(chart_elements))

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Relatório de Molas Mais Vendidas"
        filtros = f"Período: {periodo_texto}"
        add_header_footer(canvas, doc, titulo, filtros)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    # Se estamos usando um buffer de saída personalizado, retorna None
    # Se estamos usando uma resposta HTTP, retorna a resposta
    if output_buffer is not None:
        return None
    else:
        return output


def gerar_csv_molas_mais_vendidas(resultado, periodo, periodo_texto=None):
    # Cria a resposta HTTP com o tipo de conteúdo CSV
    response = HttpResponse(content_type='text/csv')

    # Define o nome do arquivo
    if periodo == 'personalizado':
        # Extrai as datas do texto do período se disponível
        if periodo_texto and 'De ' in periodo_texto:
            periodo_para_arquivo = periodo_texto.replace('/', '').replace(' ', '_')
            filename = f"molas_mais_vendidas_{periodo_para_arquivo}.csv"
        else:
            filename = f"molas_mais_vendidas_personalizado.csv"
    else:
        filename = f"molas_mais_vendidas_{periodo or 'todos'}.csv"

    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    # Cria o escritor CSV
    writer = csv.writer(response)

    # Cabeçalho com informação do período
    writer.writerow(['Relatório de Molas Mais Vendidas'])
    writer.writerow([f'Período: {periodo_texto or "Todo o período"}'])
    writer.writerow([])  # Linha em branco

    # Cabeçalho dos dados
    writer.writerow(['Pos.', 'Código', 'Cliente', 'Qtd. Vendida', 'Média Mensal', 'Variação %', 'Vs. Média Histórica'])

    # Dados
    for i, item in enumerate(resultado, 1):
        writer.writerow([
            i,
            item['mola_codigo'],
            item['mola_cliente'],
            item['total_vendido'],
            item.get('media_mensal', 0),
            f"{item.get('variacao_percentual', 0)}%",
            f"{item.get('variacao_media_historica', 0)}%"
        ])

    return response


# API para obter dados de vendas mensais para o gráfico de tendência
def vendas_mensais_json(request):
    """API para obter dados de vendas mensais para o gráfico de tendência"""
    try:
        # Obter parâmetros da requisição
        periodo = request.GET.get('periodo')
        data_inicial_str = request.GET.get('data_inicial')
        data_final_str = request.GET.get('data_final')

        # Converter strings de data para objetos date
        data_inicial = None
        data_final = None

        if data_inicial_str:
            try:
                data_inicial = datetime.strptime(data_inicial_str, '%Y-%m-%d').date()
            except ValueError:
                pass

        if data_final_str:
            try:
                data_final = datetime.strptime(data_final_str, '%Y-%m-%d').date()
            except ValueError:
                pass

        # Obter dados de vendas mensais
        vendas_mensais = Mola.obter_vendas_mensais(periodo, data_inicial, data_final)

        return JsonResponse(vendas_mensais)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# API para obter informações sobre um material específico
def material_api(request, material_id):
    """API para obter informações sobre um material específico"""
    try:
        material = Material.objects.get(pk=material_id)
        data = {
            'id': material.id,
            'nome': material.nome,
            'diametro': material.diametro,
            'material_padrao_id': material.material_padrao_id if material.material_padrao else None,
            'quantidade_estoque': float(material.quantidade_estoque),
            'ativo': material.ativo
        }
        return JsonResponse(data)
    except Material.DoesNotExist:
        return JsonResponse({'error': 'Material não encontrado'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# API para filtrar materiais por material padrão
def filtrar_materiais_api(request):
    """API para filtrar materiais por material padrão"""
    try:
        # Obter o ID do material padrão da query string
        material_padrao_id = request.GET.get('material_padrao')

        if not material_padrao_id:
            return JsonResponse({
                'success': False,
                'error': 'Parâmetro material_padrao é obrigatório'
            }, status=400)

        # Buscar o material padrão
        try:
            material_padrao = MaterialPadrao.objects.get(pk=material_padrao_id)
        except MaterialPadrao.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Material padrão não encontrado'
            }, status=404)

        # Buscar todos os materiais que são variantes desse material padrão
        materiais = Material.objects.filter(
            material_padrao=material_padrao,
            ativo=True
        )

        # Formatar os materiais para retornar no JSON
        materiais_json = []
        for material in materiais:
            materiais_json.append({
                'id': material.id,
                'nome': str(material),
                'diametro': material.diametro,
                'quantidade_estoque': float(material.quantidade_estoque)
            })

        # Ordenar materiais por nome e diâmetro numérico
        materiais_json = ordenar_materiais(materiais_json)

        return JsonResponse({
            'success': True,
            'material_padrao': {
                'id': material_padrao.id,
                'nome': material_padrao.nome,
                'diametro': material_padrao.diametro
            },
            'materiais': materiais_json
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

# Views para Alertas
def alertas_estoque(request):
    # Molas com estoque abaixo do mínimo
    molas_estoque_baixo = Mola.objects.filter(quantidade_estoque__lte=F('estoque_minimo'))

    # Materiais com estoque abaixo do mínimo
    materiais_estoque_baixo = Material.objects.filter(quantidade_estoque__lte=F('estoque_minimo'))

    return render(request, 'estoque/alertas_estoque.html', {
        'molas_estoque_baixo': molas_estoque_baixo,
        'materiais_estoque_baixo': materiais_estoque_baixo
    })





# Views para Previsão de Demanda
class PrevisaoDemandaListView(ListView):
    model = PrevisaoDemanda
    template_name = 'estoque/previsao_demanda_list.html'
    context_object_name = 'previsoes'
    paginate_by = 10
    ordering = ['-data_previsao']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['molas'] = Mola.objects.all()

        # Adicionar informações sobre métodos de previsão avançada disponíveis
        context['advanced_forecasting'] = ADVANCED_FORECASTING_AVAILABLE

        if ADVANCED_FORECASTING_AVAILABLE:
            context['statsmodels_installed'] = STATSMODELS_INSTALLED
            context['prophet_installed'] = PROPHET_INSTALLED
            context['sklearn_installed'] = SKLEARN_INSTALLED

            # Adicionar informações sobre os métodos disponíveis
            available_methods = []
            if STATSMODELS_INSTALLED:
                available_methods.append('ARIMA/SARIMA')
            if PROPHET_INSTALLED:
                available_methods.append('Prophet')
            if SKLEARN_INSTALLED:
                available_methods.append('Machine Learning')

            context['available_methods'] = available_methods

        return context


def gerar_previsao_demanda(request):
    if request.method == 'POST':
        mola_id = request.POST.get('mola_id')
        metodo = request.POST.get('metodo', 'auto')
        periodo = request.POST.get('periodo', 'M')

        if mola_id:
            # Verifica se deve usar previsão avançada ou básica
            if ADVANCED_FORECASTING_AVAILABLE:
                # Mapear método do formulário para método da API de previsão avançada
                metodo_map = {
                    'MM': 'media_movel',
                    'SE': 'arima',
                    'RL': 'prophet',
                    'AI': 'ensemble',
                    'auto': 'auto'
                }

                # Gera a previsão usando o módulo avançado
                previsao = generate_forecast(mola_id, metodo_map.get(metodo, 'auto'), periodo)
                metodo_usado = "avançado"
            else:
                # Fallback para o método básico
                previsao = PrevisaoDemanda.gerar_previsao(mola_id, metodo, periodo)
                metodo_usado = "básico"

            if previsao:
                messages.success(request, f'Previsão gerada com sucesso para {previsao.mola.codigo} usando método {metodo_usado}')

                # Adicionar informações sobre precisão se disponível
                if hasattr(previsao, 'precisao') and previsao.precisao:
                    messages.info(request, f'Precisão estimada: {previsao.precisao:.1f}%')
            else:
                messages.warning(request, 'Não foi possível gerar a previsão. Dados históricos insuficientes.')
        else:
            messages.error(request, 'Selecione uma mola para gerar a previsão.')

    return redirect('previsao-demanda-list')


def gerar_todas_previsoes(request):
    """Gera previsões para todas as molas"""
    # Obter parâmetros
    metodo = request.POST.get('metodo', 'auto')
    periodo = request.POST.get('periodo', 'M')

    # Verifica se deve usar previsão avançada ou básica
    if ADVANCED_FORECASTING_AVAILABLE:
        # Usar o módulo avançado
        previsoes = generate_all_forecasts(metodo, periodo)
        total = len(previsoes)
        metodo_usado = "avançado"

        # Calcular precisão média
        if previsoes:
            precisao_media = sum(p.precisao or 0 for p in previsoes) / total if total > 0 else 0
        else:
            precisao_media = 0
    else:
        # Fallback para o método básico
        molas = Mola.objects.all()
        total = 0
        previsoes = []

        for mola in molas:
            previsao = PrevisaoDemanda.gerar_previsao(mola.id, metodo, periodo)
            if previsao:
                previsoes.append(previsao)
                total += 1

        metodo_usado = "básico"
        precisao_media = 0

    if total > 0:
        messages.success(request, f'Previsões geradas com sucesso para {total} molas usando método {metodo_usado}.')

        # Adicionar informações sobre precisão se disponível
        if precisao_media > 0:
            messages.info(request, f'Precisão média estimada: {precisao_media:.1f}%')
    else:
        messages.warning(request, 'Não foi possível gerar previsões. Dados históricos insuficientes.')

    return redirect('previsao-demanda-list')


class PrevisaoDemandaDetailView(DetailView):
    model = PrevisaoDemanda
    template_name = 'estoque/previsao_demanda_detail.html'
    context_object_name = 'previsao'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        previsao = self.get_object()
        mola = previsao.mola

        # Histórico de vendas
        vendas = MovimentacaoEstoque.objects.filter(
            mola=mola,
            tipo='S'
        ).order_by('-data')[:12]

        # Agrupa por mês para gráfico
        vendas_por_mes = {}
        for venda in vendas:
            mes_ano = venda.data.strftime('%Y-%m')
            if mes_ano not in vendas_por_mes:
                vendas_por_mes[mes_ano] = 0
            vendas_por_mes[mes_ano] += venda.quantidade

        # Converte para formato adequado para gráfico
        labels = []
        dados = []
        for mes, quantidade in sorted(vendas_por_mes.items()):
            ano, mes = mes.split('-')
            labels.append(f"{mes}/{ano}")
            dados.append(quantidade)

        context['vendas'] = vendas
        context['labels'] = json.dumps(labels)
        context['dados'] = json.dumps(dados)
        context['previsao_valor'] = json.dumps([previsao.quantidade_prevista])

        # Adicionar informações sobre métodos de previsão avançada disponíveis
        context['advanced_forecasting'] = ADVANCED_FORECASTING_AVAILABLE

        if ADVANCED_FORECASTING_AVAILABLE:
            context['statsmodels_installed'] = STATSMODELS_INSTALLED
            context['prophet_installed'] = PROPHET_INSTALLED
            context['sklearn_installed'] = SKLEARN_INSTALLED

        return context


# Views para Análise de Obsolescência
class AnaliseObsolescenciaListView(ListView):
    model = AnaliseObsolescencia
    template_name = 'estoque/analise_obsolescencia_list.html'
    context_object_name = 'analises'
    paginate_by = 20  # Aumentado para melhor desempenho
    ordering = ['-data_analise']

    def get_queryset(self):
        # Usar uma subconsulta para obter as análises mais recentes de cada mola
        # Isso é muito mais eficiente que o método anterior
        subquery = AnaliseObsolescencia.objects.filter(
            mola=OuterRef('mola')
        ).order_by('-data_analise').values('id')[:1]

        queryset = AnaliseObsolescencia.objects.filter(
            id__in=Subquery(subquery)
        ).select_related('mola')  # Otimização: carrega dados da mola em uma única consulta

        # Filtra por classificação se especificado
        classificacao = self.request.GET.get('classificacao')
        if classificacao:
            queryset = queryset.filter(classificacao=classificacao)

        return queryset.order_by('-data_analise')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Resumo por classificação - otimizado para uma única consulta
        classificacao_counts = AnaliseObsolescencia.objects.values('classificacao').annotate(
            count=Count('classificacao')
        ).order_by('classificacao')

        # Mapear para o formato esperado
        resumo = {}
        for classificacao, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
            count = 0
            for item in classificacao_counts:
                if item['classificacao'] == classificacao:
                    count = item['count']
                    break

            resumo[classificacao] = {
                'nome': nome,
                'count': count
            }

        context['resumo'] = resumo
        context['classificacao_atual'] = self.request.GET.get('classificacao', '')

        return context


def gerar_analise_obsolescencia(request):
    """Gera análise de obsolescência para todas as molas"""
    try:
        # Usar o método de classe corretamente
        resultados = AnaliseObsolescencia.analisar_todos_itens()

        # Conta itens por classificação
        contagem = {}
        for analise in resultados:
            if analise.classificacao not in contagem:
                contagem[analise.classificacao] = 0
            contagem[analise.classificacao] += 1

        # Prepara mensagem de sucesso
        mensagem = f"Análise concluída para {len(resultados)} itens: "
        for classificacao, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
            if classificacao in contagem:
                mensagem += f"{nome}: {contagem[classificacao]}, "

        messages.success(request, mensagem[:-2])  # Remove a última vírgula e espaço

    except Exception as e:
        # Log do erro para debug
        logger.error(f"Erro ao gerar análise de obsolescência: {str(e)}")
        messages.error(request, f"Erro ao gerar análise de obsolescência: {str(e)}")

    return redirect('analise-obsolescencia-list')


class AnaliseObsolescenciaDetailView(DetailView):
    model = AnaliseObsolescencia
    template_name = 'estoque/analise_obsolescencia_detail.html'
    context_object_name = 'analise'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        analise = self.get_object()
        mola = analise.mola

        # Histórico de movimentações
        movimentacoes = MovimentacaoEstoque.objects.filter(
            mola=mola
        ).order_by('-data')[:10]

        # Outras análises da mesma mola
        outras_analises = AnaliseObsolescencia.objects.filter(
            mola=mola
        ).exclude(id=analise.id).order_by('-data_analise')[:5]

        context['movimentacoes'] = movimentacoes
        context['outras_analises'] = outras_analises

        return context


# Novas views para relatórios e impressão
def relatorio_estoque(request):
    """View para relatório de estoque personalizado"""
    if request.method == 'POST':
        form = RelatorioEstoqueForm(request.POST)
        if form.is_valid():
            # Obtém os filtros
            cliente = form.cleaned_data.get('cliente')
            codigo = form.cleaned_data.get('codigo')
            estoque_baixo = form.cleaned_data.get('estoque_baixo')
            ordenacao = form.cleaned_data.get('ordenacao') or 'codigo'

            # Filtra as molas
            molas = Mola.objects.all()

            if cliente:
                molas = molas.filter(cliente__icontains=cliente)

            if codigo:
                molas = molas.filter(codigo__icontains=codigo)

            # Conta molas com estoque baixo ANTES da ordenação (quando ainda é QuerySet)
            estoque_baixo_count = molas.filter(quantidade_estoque__lte=F('estoque_minimo')).count()

            if estoque_baixo:
                molas = molas.filter(quantidade_estoque__lte=F('estoque_minimo'))

            # Ordena os resultados
            if ordenacao == 'codigo':
                # Ordenação especial por código considerando números após a barra
                # Prioriza o nome da mola (mais confiável) e usa código como fallback
                from .utils import extrair_valor_numerico_codigo_mola, extrair_valor_numerico_nome_mola
                molas_list = list(molas)

                def chave_ordenacao(mola):
                    # Prioriza nome da mola se disponível, senão usa número após barra no código
                    if mola.nome_mola:
                        return extrair_valor_numerico_nome_mola(mola.nome_mola)
                    else:
                        return extrair_valor_numerico_codigo_mola(mola.codigo)

                molas_ordenadas = sorted(molas_list, key=chave_ordenacao)
                molas = molas_ordenadas
            else:
                molas = molas.order_by(ordenacao)

            # Formato de saída
            formato = request.POST.get('formato', 'html')

            if formato == 'pdf':
                # Gera PDF
                return gerar_pdf_estoque(molas, cliente, codigo, estoque_baixo)
            elif formato == 'csv':
                # Gera CSV
                return gerar_csv_estoque(molas, cliente, codigo, estoque_baixo)
            else:
                # Exibe na tela
                return render(request, 'estoque/relatorio_estoque.html', {
                    'form': form,
                    'molas': molas,
                    'estoque_baixo_count': estoque_baixo_count,
                    'filtro_cliente': cliente,
                    'filtro_codigo': codigo,
                    'filtro_estoque_baixo': estoque_baixo
                })
        else:
            # Se o formulário não for válido, exibe os erros
            messages.error(request, 'Erro na validação do formulário. Verifique os dados inseridos.')
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = RelatorioEstoqueForm()

    return render(request, 'estoque/relatorio_estoque.html', {'form': form})


def gerar_pdf_estoque(molas, cliente=None, codigo=None, estoque_baixo=False):
    """Gera PDF do relatório de estoque com design avançado."""
    # Cria a resposta HTTP com o tipo de conteúdo PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="relatorio_estoque.pdf"'

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    # Alterado para orientação vertical (portrait) para padronizar todos os relatórios
    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Prepara informações de filtro para o cabeçalho
    filtros_texto = []
    if cliente:
        filtros_texto.append(f"Cliente: {cliente}")
    if codigo:
        filtros_texto.append(f"Código: {codigo}")
    if estoque_baixo:
        filtros_texto.append("Apenas Estoque Baixo")

    filtros_str = " | ".join(filtros_texto) if filtros_texto else "Sem filtros aplicados"

    # Adiciona descrição
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(
        "Este relatório apresenta a situação atual do estoque de molas, "
        "incluindo informações sobre quantidade disponível e status.",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Dados da tabela
    data = [['Código', 'Cliente', 'Material', 'Diâmetro', 'Est. Atual', 'Est. Mín.', 'Status']]

    # Contadores para estatísticas
    total_molas = len(molas)
    estoque_baixo_count = 0
    estoque_total = 0

    for mola in molas:
        try:
            # Verifica status de estoque
            if mola.quantidade_estoque <= mola.estoque_minimo:
                status = "Estoque Baixo"
                estoque_baixo_count += 1
            else:
                status = "OK"

            estoque_total += mola.quantidade_estoque

            # Tratamento seguro para material
            try:
                material = mola.material.nome if mola.material else "-"
            except:
                material = "-"

            # Tratamento seguro para diâmetro
            try:
                if mola.diametro:
                    diametro = str(mola.diametro)
                elif mola.material and mola.material.diametro:
                    diametro = str(mola.material.diametro)
                else:
                    diametro = "-"
            except:
                diametro = "-"

            data.append([
                mola.codigo or "Sem código",
                mola.cliente or "Sem cliente",
                material,
                diametro,
                str(mola.quantidade_estoque),
                str(mola.estoque_minimo),
                status
            ])
        except Exception as e:
            # Em caso de erro, adiciona uma linha com informação parcial
            data.append([
                getattr(mola, 'codigo', 'Erro'),
                getattr(mola, 'cliente', 'Erro'),
                "Erro",
                "Erro",
                "Erro",
                "Erro",
                "Erro"
            ])

    # Cria a tabela com larguras ajustadas para orientação vertical
    # Código: 3.2cm, Cliente: 4.5cm, Estoque: 2.5cm, Mín.: 1.8cm, Máx.: 1.8cm, Status: 2cm, Valor: 2.5cm
    table = Table(data, colWidths=[3.2*cm, 4.5*cm, 2.5*cm, 1.8*cm, 1.8*cm, 2*cm, 2.5*cm])

    # Aplica estilo avançado à tabela
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    # Adiciona alinhamento específico para algumas colunas
    table_style = TableStyle([
        ('ALIGN', (4, 1), (6, -1), 'CENTER'),  # Centraliza colunas de estoque e status
    ])

    # Adiciona formatação condicional para status
    for i in range(1, len(data)):
        status = data[i][-1]
        if status == "Estoque Baixo":
            table_style.add('TEXTCOLOR', (6, i), (6, i), colors.red)
            table_style.add('FONTNAME', (6, i), (6, i), 'Helvetica-Bold')

    table.setStyle(table_style)
    elements.append(table)

    # Adiciona resumo e estatísticas
    elements.append(Spacer(1, 0.8*cm))
    elements.append(Paragraph("Resumo do Estoque", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Cria tabela de resumo
    resumo_data = [
        ["Total de Molas", str(total_molas)],
        ["Molas com Estoque Baixo", f"{estoque_baixo_count} ({estoque_baixo_count/total_molas*100:.1f}% do total)" if total_molas > 0 else "0"],
        ["Quantidade Total em Estoque", str(estoque_total)],
        ["Média por Item", f"{estoque_total/total_molas:.1f}" if total_molas > 0 else "0"]
    ]

    resumo_table = Table(resumo_data, colWidths=[8*cm, 8*cm])
    resumo_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
    ]))

    elements.append(resumo_table)

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Relatório de Estoque"
        add_header_footer(canvas, doc, titulo, filtros_str)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response


def gerar_csv_estoque(molas, cliente=None, codigo=None, estoque_baixo=False):
    """Gera CSV do relatório de estoque"""
    # Cria a resposta HTTP com o tipo de conteúdo CSV
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="relatorio_estoque.csv"'

    # Cria o escritor CSV
    writer = csv.writer(response)

    # Cabeçalho
    writer.writerow(['Código', 'Cliente', 'Material', 'Diâmetro', 'Est. Atual', 'Est. Mín.', 'Status'])

    # Dados
    for mola in molas:
        status = "Estoque Baixo" if mola.quantidade_estoque <= mola.estoque_minimo else "OK"
        material = mola.material.nome if mola.material else "-"
        # Tratamento seguro para diâmetro
        if mola.diametro:
            diametro = mola.diametro
        elif mola.material and mola.material.diametro:
            diametro = mola.material.diametro
        else:
            diametro = "-"

        writer.writerow([
            mola.codigo,
            mola.cliente,
            material,
            diametro,
            mola.quantidade_estoque,
            mola.estoque_minimo,
            status
        ])

    return response


def previsao_demanda_pdf(request, pk):
    """Gera PDF da previsão de demanda com design avançado."""
    previsao = get_object_or_404(PrevisaoDemanda, pk=pk)
    mola = previsao.mola

    # Obtém dados para o gráfico
    vendas = MovimentacaoEstoque.objects.filter(
        mola=mola,
        tipo='S'
    ).order_by('-data')[:12]

    # Agrupa por mês para gráfico
    vendas_por_mes = {}
    for venda in vendas:
        mes_ano = venda.data.strftime('%Y-%m')
        if mes_ano not in vendas_por_mes:
            vendas_por_mes[mes_ano] = 0
        vendas_por_mes[mes_ano] += venda.quantidade

    # Converte para formato adequado para gráfico
    labels = []
    dados = []
    for mes, quantidade in sorted(vendas_por_mes.items()):
        ano, mes = mes.split('-')
        labels.append(f"{mes}/{ano}")
        dados.append(quantidade)

    # Cria a resposta HTTP com o tipo de conteúdo PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="previsao_demanda_{previsao.id}.pdf"'

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Adiciona informações do relatório
    elements.append(Spacer(1, 0.5*cm))

    # Informações da previsão
    elements.append(Paragraph("Informações da Previsão", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    info_data = [
        ['Mola:', mola.codigo],
        ['Cliente:', mola.cliente],
        ['Data da Previsão:', previsao.data_previsao.strftime('%d/%m/%Y')],
        ['Período:', previsao.get_periodo_display()],
        ['Método:', previsao.get_metodo_display()],
        ['Quantidade Prevista:', str(previsao.quantidade_prevista)],
        ['Validade:', f"{previsao.data_inicio.strftime('%d/%m/%Y')} a {previsao.data_fim.strftime('%d/%m/%Y')}"]
    ]

    if previsao.precisao:
        info_data.append(['Precisão:', f"{previsao.precisao:.2f}%"])

    info_table = Table(info_data, colWidths=[5*cm, 12*cm])
    info_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ('WORDWRAP', (0, 0), (-1, -1), True),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
    ]))

    elements.append(info_table)
    elements.append(Spacer(1, 0.8*cm))

    # Recomendações
    elements.append(Paragraph("Recomendações", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Cria um box de destaque para as recomendações
    recomendacao_texto = []
    recomendacao_texto.append(Paragraph(f"Com base na previsão de demanda de {previsao.quantidade_prevista} unidades para o próximo período:", styles['normal']))
    recomendacao_texto.append(Spacer(1, 0.2*cm))

    if mola.quantidade_estoque < previsao.quantidade_prevista:
        deficit = previsao.quantidade_prevista - mola.quantidade_estoque
        percentual = (deficit / previsao.quantidade_prevista) * 100

        recomendacao_texto.append(Paragraph(f"• O estoque atual de {mola.quantidade_estoque} unidades é <b>insuficiente</b> para atender à demanda prevista.", styles['normal']))
        recomendacao_texto.append(Paragraph(f"• Déficit de {deficit} unidades ({percentual:.1f}% da demanda prevista).", styles['normal']))
        recomendacao_texto.append(Spacer(1, 0.2*cm))
        recomendacao_texto.append(Paragraph(f"• <b>Recomendação:</b> Produzir pelo menos {deficit} unidades adicionais.", styles['highlight']))
    else:
        excesso = mola.quantidade_estoque - previsao.quantidade_prevista
        percentual = (excesso / previsao.quantidade_prevista) * 100 if previsao.quantidade_prevista > 0 else 100

        recomendacao_texto.append(Paragraph(f"• O estoque atual de {mola.quantidade_estoque} unidades é <b>suficiente</b> para atender à demanda prevista.", styles['normal']))
        recomendacao_texto.append(Paragraph(f"• Excesso de {excesso} unidades ({percentual:.1f}% acima da demanda prevista).", styles['normal']))
        recomendacao_texto.append(Spacer(1, 0.2*cm))
        recomendacao_texto.append(Paragraph(f"• <b>Recomendação:</b> Manter o nível de estoque atual.", styles['highlight']))

    # Cria um box para as recomendações
    recomendacao_box = Table([[recomendacao_texto]], colWidths=[17*cm])
    recomendacao_box.setStyle(TableStyle([
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor('#CCCCCC')),
        ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#F9F9F9')),
        ('LEFTPADDING', (0, 0), (-1, -1), 12),
        ('RIGHTPADDING', (0, 0), (-1, -1), 12),
        ('TOPPADDING', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))

    elements.append(recomendacao_box)
    elements.append(Spacer(1, 0.8*cm))

    # Adiciona gráfico se houver dados suficientes
    if len(dados) > 1:
        elements.append(Paragraph("Análise de Vendas e Previsão", styles['subtitle']))
        elements.append(Spacer(1, 0.3*cm))

        try:
            # Cria um gráfico avançado de forma segura
            fig, ax = plt.subplots(figsize=(8, 4))

            # Estilo personalizado
            try:
                plt.style.use('seaborn-v0_8-darkgrid')
            except:
                plt.style.use('default')

            # Cores personalizadas
            bar_color = '#bb86fc'  # Roxo/lilás
            line_color = '#cf6679'  # Vermelho/rosa

            # Criar gráfico de barras
            bars = ax.bar(labels, dados, color=bar_color, alpha=0.7)

            # Adicionar linha de previsão
            ax.axhline(y=previsao.quantidade_prevista, color=line_color, linestyle='--', linewidth=2, label='Previsão')

            # Adicionar valor em cima de cada barra
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{int(height)}', ha='center', va='bottom', fontweight='bold')

            # Adicionar valor da previsão
            ax.text(len(labels) - 0.5, previsao.quantidade_prevista + 0.5,
                   f'Previsão: {previsao.quantidade_prevista}',
                   color=line_color, fontweight='bold', ha='right')

            # Personalizar o gráfico
            ax.set_xlabel('Período', fontsize=12)
            ax.set_ylabel('Quantidade', fontsize=12)
            ax.set_title('Histórico de Vendas e Previsão de Demanda', fontsize=14, fontweight='bold', pad=20)
            ax.tick_params(axis='x', rotation=45)
            ax.legend()
            ax.grid(axis='y', linestyle='--', alpha=0.7)

            # Ajustar layout com padding extra
            plt.tight_layout(pad=2.0)

            # Salva o gráfico em um buffer
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            img_buffer.seek(0)

            # Adiciona o gráfico ao PDF
            img = RLImage(img_buffer, width=16*cm, height=9*cm)
            elements.append(img)
            elements.append(Spacer(1, 0.5*cm))

            # Fecha a figura para liberar memória
            plt.close(fig)
            plt.clf()

        except Exception as e:
            print(f"Erro ao criar gráfico de previsão: {e}")
            elements.append(Paragraph("Erro ao gerar gráfico de previsão.", styles['info']))

    # Histórico de vendas
    elements.append(Paragraph("Histórico de Movimentações", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    if vendas:
        vendas_data = [['Data', 'Tipo', 'Quantidade', 'Ordem']]

        for venda in vendas:
            tipo = "Entrada" if venda.tipo == 'E' else "Saída"
            vendas_data.append([
                venda.data.strftime('%d/%m/%Y'),
                tipo,
                str(venda.quantidade),
                venda.ordem_venda or "--"
            ])

        vendas_table = Table(vendas_data, colWidths=[4*cm, 3.5*cm, 3.5*cm, 6*cm])
        vendas_table.setStyle(create_table_style(has_header=True, alternating_colors=True))

        elements.append(vendas_table)
    else:
        elements.append(Paragraph("Nenhuma movimentação registrada.", styles['info']))

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = f"Previsão de Demanda - {mola.codigo}"
        filtros = f"Cliente: {mola.cliente} | Método: {previsao.get_metodo_display()}"
        add_header_footer(canvas, doc, titulo, filtros)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response


def analise_obsolescencia_pdf(request):
    """Gera PDF da análise de obsolescência com design avançado."""
    # Obtém os filtros
    classificacao = request.GET.get('classificacao')

    # Filtra as análises
    analises = AnaliseObsolescencia.objects.all()

    if classificacao:
        analises = analises.filter(classificacao=classificacao)

    # Agrupa por mola (pega apenas a análise mais recente de cada mola)
    molas_ids = analises.values_list('mola_id', flat=True).distinct()
    resultado = []

    for mola_id in molas_ids:
        analise = analises.filter(mola_id=mola_id).order_by('-data_analise').first()
        if analise:
            resultado.append(analise)

    # Cria a resposta HTTP com o tipo de conteúdo PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="analise_obsolescencia.pdf"'

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    # Alterado para formato vertical (retrato)
    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Prepara informações de filtro para o cabeçalho
    filtro_texto = ""
    if classificacao:
        for c, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
            if c == classificacao:
                filtro_texto = f"Filtro: {nome}"
                break

    # Adiciona descrição
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(
        "Este relatório apresenta a análise de obsolescência do estoque, "
        "identificando itens sem movimentação e classificando-os por nível de risco.",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Resumo por classificação
    elements.append(Paragraph("Resumo por Classificação", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Calcula o resumo por classificação
    resumo = {}
    total_itens = 0
    total_valor = 0

    for c, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
        analises_classe = AnaliseObsolescencia.objects.filter(
            classificacao=c,
            mola_id__in=molas_ids
        ).order_by('-data_analise')

        # Pega apenas a análise mais recente de cada mola
        molas_classe = set()
        analises_unicas = []

        for analise in analises_classe:
            if analise.mola_id not in molas_classe:
                molas_classe.add(analise.mola_id)
                analises_unicas.append(analise)

        count = len(analises_unicas)
        valor = sum(a.valor_estoque for a in analises_unicas)

        total_itens += count
        total_valor += valor

        resumo[c] = {
            'nome': nome,
            'count': count,
            'valor': valor
        }

    # Cria dados para o gráfico
    labels = []
    counts = []
    cores = []

    # Cores para cada classificação
    cores_map = {
        'A': '#bb86fc',  # Roxo/lilás (ativo)
        'B': '#03dac6',  # Turquesa (baixa rotatividade)
        'C': '#ffab40',  # Laranja (crítico)
        'O': '#cf6679',  # Vermelho (obsoleto)
    }

    for c, info in sorted(resumo.items()):
        if info['count'] > 0:
            labels.append(info['nome'])
            counts.append(info['count'])
            cores.append(cores_map.get(c, '#bb86fc'))

    # Cria tabela de resumo - removidas colunas "Valor em R$" e "% do Total"
    resumo_data = [['Classificação', 'Quantidade']]

    for c, info in sorted(resumo.items()):
        resumo_data.append([
            info['nome'],
            str(info['count'])
        ])

    # Adiciona linha de total
    resumo_data.append([
        'TOTAL',
        str(total_itens)
    ])

    # Ajustadas larguras para formato vertical
    resumo_table = Table(resumo_data, colWidths=[10*cm, 6*cm])

    # Estilo básico da tabela
    resumo_style = create_table_style(has_header=True, alternating_colors=True)

    # Adiciona estilo para a linha de total
    resumo_style.add('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f0f0f0'))
    resumo_style.add('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold')

    resumo_table.setStyle(resumo_style)
    elements.append(resumo_table)

    # Adiciona gráficos se houver dados
    if counts:
        elements.append(Spacer(1, 1*cm))
        elements.append(Paragraph("Distribuição por Classificação", styles['subtitle']))
        elements.append(Spacer(1, 0.3*cm))

        try:
            # Cria gráfico de pizza para distribuição por classificação de forma segura
            fig, ax = plt.subplots(figsize=(7, 7))  # Dimensões quadradas para evitar deformação

            ax.pie(counts, labels=labels, autopct='%1.1f%%', startangle=90, colors=cores)
            ax.axis('equal')  # Mantém a proporção circular
            ax.set_title('Distribuição por Quantidade', fontsize=14, fontweight='bold', pad=20)

            # Adiciona um pouco mais de espaço ao redor do gráfico
            plt.tight_layout(pad=2.0)

            # Salva o gráfico em um buffer
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            img_buffer.seek(0)

            # Adiciona o gráfico ao PDF com dimensões quadradas
            img = RLImage(img_buffer, width=12*cm, height=12*cm)
            elements.append(img)

            # Fecha a figura para liberar memória
            plt.close(fig)
            plt.clf()

        except Exception as e:
            print(f"Erro ao criar gráfico de distribuição: {e}")
            elements.append(Paragraph("Erro ao gerar gráfico de distribuição.", styles['info']))

    elements.append(Spacer(1, 0.8*cm))

    # Lista detalhada de itens REORGANIZADA POR CLASSIFICAÇÕES
    elements.append(Paragraph("Lista Detalhada de Itens por Classificação", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Organizar análises por classificação e ordenar dentro de cada grupo
    analises_por_classificacao = {}

    # Agrupar por classificação
    for analise in resultado:
        classificacao = analise.classificacao
        if classificacao not in analises_por_classificacao:
            analises_por_classificacao[classificacao] = []
        analises_por_classificacao[classificacao].append(analise)

    # Ordenar dentro de cada classificação
    for classificacao, analises in analises_por_classificacao.items():
        # Calcular total de vendas para cada mola para usar como critério de ordenação
        analises_com_vendas = []
        for analise in analises:
            # Calcular total de vendas históricas da mola
            total_vendas = Mola._calcular_total_vendas(
                analise.mola.id,
                timezone.now().date() - timezone.timedelta(days=365*2),  # Últimos 2 anos
                timezone.now().date()
            )
            analises_com_vendas.append((analise, total_vendas))

        # Ordenar por: 1º critério = rotatividade (menor dias sem movimentação = maior rotatividade)
        # 2º critério = quantidade de vendas (maior primeiro) em caso de empate
        analises_com_vendas.sort(key=lambda x: (x[0].dias_sem_movimentacao, -x[1]))
        analises_por_classificacao[classificacao] = [item[0] for item in analises_com_vendas]

    # Ordem das classificações para exibição
    ordem_classificacoes = ['A', 'B', 'C', 'D', 'O']
    nomes_classificacoes = {
        'A': 'ALTA ROTATIVIDADE',
        'B': 'MÉDIA ROTATIVIDADE',
        'C': 'BAIXA ROTATIVIDADE',
        'D': 'SEM VENDAS',
        'O': 'OBSOLETO'
    }

    # Gerar seções por classificação
    for classificacao in ordem_classificacoes:
        if classificacao in analises_por_classificacao and analises_por_classificacao[classificacao]:
            # Título da seção
            elements.append(Spacer(1, 0.5*cm))
            elements.append(Paragraph(nomes_classificacoes[classificacao], styles['subtitle']))
            elements.append(Spacer(1, 0.2*cm))

            # Dados da tabela para esta classificação
            data = [['Código', 'Cliente', 'Dias Sem Mov.', 'Última Mov.', 'Estoque']]

            for analise in analises_por_classificacao[classificacao]:
                ultima_mov = analise.ultima_movimentacao.strftime('%d/%m/%Y') if analise.ultima_movimentacao else "Nunca"

                data.append([
                    analise.mola.codigo,
                    analise.mola.cliente,
                    str(analise.dias_sem_movimentacao),
                    ultima_mov,
                    str(analise.mola.quantidade_estoque)
                ])

            # Cria a tabela com larguras ajustadas para formato vertical
            # Código: 3.5cm, Cliente: 6cm, Dias: 2.5cm, Última Mov.: 3cm, Estoque: 2.5cm
            table = Table(data, colWidths=[3.5*cm, 6*cm, 2.5*cm, 3*cm, 2.5*cm])

            # Aplica estilo avançado à tabela
            table.setStyle(create_table_style(has_header=True, alternating_colors=True))

            # Adiciona formatação condicional baseada na classificação
            if classificacao == 'O':  # Obsoleto
                table_style = TableStyle([
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.red),  # Cabeçalho em vermelho
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold')
                ])
                table.setStyle(table_style)
            elif classificacao == 'D':  # Sem Vendas
                table_style = TableStyle([
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.orange),  # Cabeçalho em laranja
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold')
                ])
                table.setStyle(table_style)
            elif classificacao == 'C':  # Baixa Rotatividade
                table_style = TableStyle([
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#ffab40')),  # Cabeçalho em amarelo
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold')
                ])
                table.setStyle(table_style)

            elements.append(table)
            elements.append(Spacer(1, 0.3*cm))

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Análise de Obsolescência"
        add_header_footer(canvas, doc, titulo, filtro_texto)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response


def analise_obsolescencia_csv(request):
    """Gera CSV da análise de obsolescência"""
    # Obtém os filtros
    classificacao = request.GET.get('classificacao')

    # Filtra as análises
    analises = AnaliseObsolescencia.objects.all()

    if classificacao:
        analises = analises.filter(classificacao=classificacao)

    # Agrupa por mola (pega apenas a análise mais recente de cada mola)
    molas_ids = analises.values_list('mola_id', flat=True).distinct()
    resultado = []

    for mola_id in molas_ids:
        analise = analises.filter(mola_id=mola_id).order_by('-data_analise').first()
        if analise:
            resultado.append(analise)

    # Cria a resposta HTTP com o tipo de conteúdo CSV
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="analise_obsolescencia.csv"'

    # Cria o escritor CSV
    writer = csv.writer(response)

    # Cabeçalho - removida coluna "Valor (R$)"
    writer.writerow(['Código', 'Cliente', 'Classificação', 'Dias Sem Mov.', 'Última Mov.', 'Estoque'])

    # Dados
    for analise in resultado:
        ultima_mov = analise.ultima_movimentacao.strftime('%d/%m/%Y') if analise.ultima_movimentacao else "Nunca"

        writer.writerow([
            analise.mola.codigo,
            analise.mola.cliente,
            analise.get_classificacao_display(),
            analise.dias_sem_movimentacao,
            ultima_mov,
            analise.mola.quantidade_estoque
        ])

    return response


def relatorio_vendas_por_mola(request):
    """Relatório de vendas por mola específica"""
    if request.method == 'POST':
        form = RelatorioVendasPorMolaForm(request.POST)
        if form.is_valid():
            mola = form.cleaned_data['mola']  # Já é um objeto Mola
            formato = form.cleaned_data.get('formato', 'tela')
            usar_periodo = form.cleaned_data.get('usar_periodo_personalizado', False)

            if not mola:
                messages.error(request, 'Mola não encontrada.')
                return render(request, 'estoque/relatorio_vendas_por_mola.html', {'form': form})

            # Processar período personalizado se selecionado
            data_inicial = None
            data_final = None
            periodo_texto = "Todo o período"

            if usar_periodo:
                mes_inicial = form.cleaned_data.get('mes_inicial')
                ano_inicial = form.cleaned_data.get('ano_inicial')
                mes_final = form.cleaned_data.get('mes_final')
                ano_final = form.cleaned_data.get('ano_final')

                if mes_inicial and ano_inicial and mes_final and ano_final:
                    from datetime import date
                    from calendar import monthrange

                    # Data inicial: primeiro dia do mês inicial
                    data_inicial = date(int(ano_inicial), int(mes_inicial), 1)

                    # Data final: último dia do mês final
                    ultimo_dia = monthrange(int(ano_final), int(mes_final))[1]
                    data_final = date(int(ano_final), int(mes_final), ultimo_dia)

                    # Texto do período
                    meses_nomes = dict(form.MESES_CHOICES)
                    if mes_inicial == mes_final and ano_inicial == ano_final:
                        periodo_texto = f"{meses_nomes[mes_inicial]} de {ano_inicial}"
                    else:
                        periodo_texto = f"{meses_nomes[mes_inicial]}/{ano_inicial} até {meses_nomes[mes_final]}/{ano_final}"
                else:
                    messages.error(request, 'Por favor, preencha todos os campos do período personalizado.')
                    return render(request, 'estoque/relatorio_vendas_por_mola.html', {'form': form})

            # Obter vendas da mola (com ou sem filtro de período)
            vendas = obter_vendas_mola_especifica(mola, data_inicial, data_final)

            if formato == 'pdf':
                return gerar_pdf_vendas_por_mola(mola, vendas, periodo_texto)
            elif formato == 'csv':
                return gerar_csv_vendas_por_mola(mola, vendas, periodo_texto)
            else:
                return render(request, 'estoque/relatorio_vendas_por_mola.html', {
                    'form': form,
                    'mola': mola,
                    'vendas': vendas,
                    'periodo_texto': periodo_texto,
                    'usar_periodo': usar_periodo
                })
    else:
        form = RelatorioVendasPorMolaForm()

    return render(request, 'estoque/relatorio_vendas_por_mola.html', {'form': form})


def obter_vendas_mola_especifica(mola, data_inicial=None, data_final=None):
    """Obtém todas as vendas de uma mola específica, opcionalmente filtradas por período"""
    from collections import defaultdict
    from datetime import datetime

    vendas = []

    # Vendas através de movimentações de estoque (saídas)
    movimentacoes_query = MovimentacaoEstoque.objects.filter(
        mola=mola,
        tipo='S'
    )

    # Aplicar filtro de período se fornecido
    if data_inicial:
        movimentacoes_query = movimentacoes_query.filter(data__gte=data_inicial)
    if data_final:
        movimentacoes_query = movimentacoes_query.filter(data__lte=data_final)

    movimentacoes = movimentacoes_query.order_by('-data')

    for mov in movimentacoes:
        vendas.append({
            'data': mov.data.date() if hasattr(mov.data, 'date') else mov.data,
            'tipo': 'Movimentação de Estoque',
            'numero_pedido': mov.ordem_venda or mov.observacao or 'N/A',
            'quantidade': mov.quantidade,
            'observacoes': mov.observacao or ''
        })

    # Vendas através de itens de pedido atendidos
    itens_pedido_query = ItemPedido.objects.filter(
        mola=mola,
        atendido=True
    ).select_related('pedido')

    # Aplicar filtro de período se fornecido
    if data_inicial:
        itens_pedido_query = itens_pedido_query.filter(pedido__data_pedido__gte=data_inicial)
    if data_final:
        itens_pedido_query = itens_pedido_query.filter(pedido__data_pedido__lte=data_final)

    itens_pedido = itens_pedido_query.order_by('-pedido__data_pedido')

    for item in itens_pedido:
        vendas.append({
            'data': item.pedido.data_pedido,
            'tipo': 'Pedido',
            'numero_pedido': f'Pedido #{item.pedido.numero_pedido}',
            'quantidade': item.quantidade,
            'observacoes': item.pedido.observacao or ''
        })

    # Ordenar por data decrescente (mais recente primeiro)
    vendas.sort(key=lambda x: x['data'], reverse=True)

    # Se há filtro de período, organizar por mês
    if data_inicial or data_final:
        return organizar_vendas_por_mes(vendas, data_inicial, data_final)

    return vendas


def organizar_vendas_por_mes(vendas, data_inicial=None, data_final=None):
    """Organiza as vendas por mês e calcula totais, incluindo meses sem vendas no período"""
    from collections import defaultdict
    from datetime import date
    from calendar import monthrange

    vendas_por_mes = defaultdict(list)

    # Nomes dos meses em português
    meses_nomes = {
        1: 'Janeiro', 2: 'Fevereiro', 3: 'Março', 4: 'Abril',
        5: 'Maio', 6: 'Junho', 7: 'Julho', 8: 'Agosto',
        9: 'Setembro', 10: 'Outubro', 11: 'Novembro', 12: 'Dezembro'
    }

    # Agrupar vendas por mês/ano
    for venda in vendas:
        data_venda = venda['data']
        chave_mes = f"{data_venda.year}-{data_venda.month:02d}"
        vendas_por_mes[chave_mes].append(venda)

    # Se há período definido, gerar todos os meses do intervalo
    meses_periodo = set()
    if data_inicial and data_final:
        # Gerar todos os meses entre data_inicial e data_final
        ano_atual = data_inicial.year
        mes_atual = data_inicial.month

        while (ano_atual < data_final.year) or (ano_atual == data_final.year and mes_atual <= data_final.month):
            chave_mes = f"{ano_atual}-{mes_atual:02d}"
            meses_periodo.add(chave_mes)

            # Avançar para o próximo mês
            mes_atual += 1
            if mes_atual > 12:
                mes_atual = 1
                ano_atual += 1
    else:
        # Se não há período, usar apenas os meses que têm vendas
        meses_periodo = set(vendas_por_mes.keys())

    # Organizar dados por mês
    resultado = []
    for chave_mes in sorted(meses_periodo, reverse=True):
        ano, mes = chave_mes.split('-')
        nome_mes = meses_nomes.get(int(mes), f"Mês {mes}")

        vendas_do_mes = vendas_por_mes.get(chave_mes, [])
        total_quantidade = sum(v['quantidade'] for v in vendas_do_mes)
        total_transacoes = len(vendas_do_mes)

        resultado.append({
            'mes_ano': f"{nome_mes} {ano}",
            'vendas': sorted(vendas_do_mes, key=lambda x: x['data'], reverse=True),
            'total_quantidade': total_quantidade,
            'total_transacoes': total_transacoes,
            'ano': int(ano),
            'mes': int(mes),
            'tem_vendas': len(vendas_do_mes) > 0
        })

    return resultado


def gerar_pdf_vendas_por_mola(mola, vendas, periodo_texto="Todo o período"):
    """Gera PDF do relatório de vendas por mola"""
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="vendas_mola_{mola.codigo}.pdf"'

    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    styles = get_styles()

    # Informações da mola
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(f"Mola: {mola.codigo} - {mola.cliente}", styles['subtitle']))
    elements.append(Paragraph(f"Período: {periodo_texto}", styles['normal']))
    elements.append(Spacer(1, 0.3*cm))

    # Verificar se os dados estão organizados por mês
    if vendas and isinstance(vendas[0], dict) and 'mes_ano' in vendas[0]:
        # Dados organizados por mês
        total_geral_quantidade = 0
        total_geral_transacoes = 0

        for mes_data in vendas:
            # Título do mês (menor e alinhado à esquerda)
            if mes_data['tem_vendas']:
                titulo_mes = f"{mes_data['mes_ano']} - {mes_data['total_quantidade']} unidades em {mes_data['total_transacoes']} transações"
            else:
                titulo_mes = f"{mes_data['mes_ano']}"

            elements.append(Paragraph(titulo_mes, styles['normal']))
            elements.append(Spacer(1, 0.1*cm))

            if mes_data['tem_vendas']:
                # Tabela das vendas do mês
                data = [['Data', 'Tipo', 'Número do Pedido', 'Quantidade', 'Observações']]

                for venda in mes_data['vendas']:
                    data.append([
                        venda['data'].strftime('%d/%m/%Y'),
                        venda['tipo'],
                        venda['numero_pedido'],
                        str(venda['quantidade']),
                        venda['observacoes'][:40] + '...' if len(venda['observacoes']) > 40 else venda['observacoes']
                    ])

                # Criar tabela
                table = Table(data, colWidths=[2.2*cm, 2.8*cm, 3.2*cm, 2.2*cm, 4.6*cm])
                table.setStyle(create_table_style(has_header=True, alternating_colors=True))
                elements.append(table)
            else:
                # Texto alinhado à esquerda para meses sem vendas
                elements.append(Paragraph("    Nenhuma venda registrada neste mês", styles['info']))

            elements.append(Spacer(1, 0.3*cm))

            total_geral_quantidade += mes_data['total_quantidade']
            total_geral_transacoes += mes_data['total_transacoes']

        # Resumo geral
        elements.append(Spacer(1, 0.5*cm))
        elements.append(Paragraph("RESUMO GERAL", styles['subtitle']))
        elements.append(Paragraph(f"Total de transações: {total_geral_transacoes}", styles['info']))
        elements.append(Paragraph(f"Quantidade total vendida: {total_geral_quantidade}", styles['info']))

    else:
        # Dados tradicionais (sem agrupamento)
        elements.append(Paragraph(
            "Este relatório apresenta todas as vendas/saídas da mola selecionada, "
            "incluindo movimentações de estoque e itens de pedidos atendidos.",
            styles['normal']
        ))
        elements.append(Spacer(1, 0.5*cm))

        # Dados da tabela
        data = [['Data', 'Tipo', 'Número do Pedido', 'Quantidade', 'Observações']]

        for venda in vendas:
            data.append([
                venda['data'].strftime('%d/%m/%Y'),
                venda['tipo'],
                venda['numero_pedido'],
                str(venda['quantidade']),
                venda['observacoes'][:50] + '...' if len(venda['observacoes']) > 50 else venda['observacoes']
            ])

        # Cria a tabela
        table = Table(data, colWidths=[2.5*cm, 3*cm, 3.5*cm, 2.5*cm, 5*cm])
        table.setStyle(create_table_style(has_header=True, alternating_colors=True))

        elements.append(table)
        elements.append(Spacer(1, 0.5*cm))

        # Resumo
        total_vendido = sum(venda['quantidade'] for venda in vendas)
        elements.append(Paragraph(f"Total de vendas: {len(vendas)}", styles['info']))
        elements.append(Paragraph(f"Quantidade total vendida: {total_vendido}", styles['info']))

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = f"Relatório de Vendas - Mola {mola.codigo}"
        filtros = f"Cliente: {mola.cliente} | {periodo_texto}"
        add_header_footer(canvas, doc, titulo, filtros)

    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)
    return response


def gerar_csv_vendas_por_mola(mola, vendas, periodo_texto="Todo o período"):
    """Gera CSV do relatório de vendas por mola"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="vendas_mola_{mola.codigo}.csv"'

    writer = csv.writer(response)

    # Cabeçalho com informação da mola
    writer.writerow([f'Relatório de Vendas - Mola {mola.codigo}'])
    writer.writerow([f'Cliente: {mola.cliente}'])
    writer.writerow([f'Período: {periodo_texto}'])
    writer.writerow([])  # Linha em branco

    # Verificar se os dados estão organizados por mês
    if vendas and isinstance(vendas[0], dict) and 'mes_ano' in vendas[0]:
        # Dados organizados por mês
        for mes_data in vendas:
            # Título do mês mais compacto
            if mes_data['tem_vendas']:
                writer.writerow([f'{mes_data["mes_ano"]} - {mes_data["total_quantidade"]} unidades em {mes_data["total_transacoes"]} transações'])
                writer.writerow([])

                # Cabeçalho dos dados
                writer.writerow(['Data', 'Tipo', 'Número do Pedido', 'Quantidade', 'Observações'])

                # Dados do mês
                for venda in mes_data['vendas']:
                    writer.writerow([
                        venda['data'].strftime('%d/%m/%Y'),
                        venda['tipo'],
                        venda['numero_pedido'],
                        venda['quantidade'],
                        venda['observacoes']
                    ])
            else:
                writer.writerow([f'{mes_data["mes_ano"]}'])
                writer.writerow(['Nenhuma venda registrada neste mês'])

            writer.writerow([])  # Linha em branco entre meses

        # Resumo geral
        total_geral_quantidade = sum(mes['total_quantidade'] for mes in vendas)
        total_geral_transacoes = sum(mes['total_transacoes'] for mes in vendas)
        writer.writerow(['=== RESUMO GERAL ==='])
        writer.writerow([f'Total de transações: {total_geral_transacoes}'])
        writer.writerow([f'Quantidade total vendida: {total_geral_quantidade}'])

    else:
        # Dados tradicionais (sem agrupamento)
        # Cabeçalho dos dados
        writer.writerow(['Data', 'Tipo', 'Número do Pedido', 'Quantidade', 'Observações'])

        # Dados
        for venda in vendas:
            writer.writerow([
                venda['data'].strftime('%d/%m/%Y'),
                venda['tipo'],
                venda['numero_pedido'],
                venda['quantidade'],
                venda['observacoes']
            ])

    return response


def relatorio_molas_nao_vendidas(request):
    """Relatório de molas não vendidas no último mês"""
    if request.method == 'POST':
        form = RelatorioMolasNaoVendidasForm(request.POST)
        if form.is_valid():
            periodo_dias = form.cleaned_data['periodo_dias']
            formato = form.cleaned_data.get('formato', 'tela')

            # Calcular data de corte
            data_corte = timezone.now().date() - timedelta(days=periodo_dias)

            # Obter todas as molas
            todas_molas = Mola.objects.all()

            molas_nao_vendidas = []

            for mola in todas_molas:
                # Verificar se teve vendas no período
                teve_venda_movimentacao = MovimentacaoEstoque.objects.filter(
                    mola=mola,
                    tipo='S',
                    data__gte=data_corte
                ).exists()

                teve_venda_pedido = ItemPedido.objects.filter(
                    mola=mola,
                    atendido=True,
                    pedido__data_pedido__gte=data_corte
                ).exists()

                if not teve_venda_movimentacao and not teve_venda_pedido:
                    # Calcular última venda
                    ultima_mov_saida = MovimentacaoEstoque.objects.filter(
                        mola=mola,
                        tipo='S'
                    ).order_by('-data').first()

                    ultimo_item_pedido = ItemPedido.objects.filter(
                        mola=mola,
                        atendido=True
                    ).select_related('pedido').order_by('-pedido__data_pedido').first()

                    ultima_venda = None
                    if ultima_mov_saida and ultimo_item_pedido:
                        data_mov = ultima_mov_saida.data.date() if hasattr(ultima_mov_saida.data, 'date') else ultima_mov_saida.data
                        data_pedido = ultimo_item_pedido.pedido.data_pedido.date() if hasattr(ultimo_item_pedido.pedido.data_pedido, 'date') else ultimo_item_pedido.pedido.data_pedido
                        ultima_venda = max(data_mov, data_pedido)
                    elif ultima_mov_saida:
                        ultima_venda = ultima_mov_saida.data.date() if hasattr(ultima_mov_saida.data, 'date') else ultima_mov_saida.data
                    elif ultimo_item_pedido:
                        ultima_venda = ultimo_item_pedido.pedido.data_pedido.date() if hasattr(ultimo_item_pedido.pedido.data_pedido, 'date') else ultimo_item_pedido.pedido.data_pedido

                    dias_sem_venda = (timezone.now().date() - ultima_venda).days if ultima_venda else None

                    molas_nao_vendidas.append({
                        'mola': mola,
                        'ultima_venda': ultima_venda,
                        'dias_sem_venda': dias_sem_venda,
                        'estoque_atual': mola.quantidade_estoque
                    })

            # Ordenar por dias sem venda (decrescente)
            molas_nao_vendidas.sort(key=lambda x: x['dias_sem_venda'] or 0, reverse=True)

            if formato == 'pdf':
                return gerar_pdf_molas_nao_vendidas(molas_nao_vendidas, periodo_dias)
            elif formato == 'csv':
                return gerar_csv_molas_nao_vendidas(molas_nao_vendidas, periodo_dias)
            else:
                return render(request, 'estoque/relatorio_molas_nao_vendidas.html', {
                    'form': form,
                    'molas_nao_vendidas': molas_nao_vendidas,
                    'periodo_dias': periodo_dias
                })
    else:
        form = RelatorioMolasNaoVendidasForm()

    return render(request, 'estoque/relatorio_molas_nao_vendidas.html', {'form': form})


def gerar_pdf_molas_nao_vendidas(molas_nao_vendidas, periodo_dias):
    """Gera PDF do relatório de molas não vendidas"""
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="molas_nao_vendidas_{periodo_dias}_dias.pdf"'

    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    styles = get_styles()

    # Informações do relatório
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(f"Período analisado: Últimos {periodo_dias} dias", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Descrição
    elements.append(Paragraph(
        f"Este relatório apresenta as molas que não tiveram vendas nos últimos {periodo_dias} dias, "
        "ordenadas por tempo sem vendas (maior para menor).",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Dados da tabela
    data = [['Código', 'Cliente', 'Última Venda', 'Dias Sem Venda', 'Estoque Atual']]

    for item in molas_nao_vendidas:
        ultima_venda_str = item['ultima_venda'].strftime('%d/%m/%Y') if item['ultima_venda'] else 'Nunca'
        dias_sem_venda_str = str(item['dias_sem_venda']) if item['dias_sem_venda'] else 'N/A'

        data.append([
            item['mola'].codigo,
            item['mola'].cliente,
            ultima_venda_str,
            dias_sem_venda_str,
            str(item['estoque_atual'])
        ])

    # Cria a tabela
    table = Table(data, colWidths=[3*cm, 5*cm, 2.5*cm, 2.5*cm, 2.5*cm])
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    elements.append(table)
    elements.append(Spacer(1, 0.5*cm))

    # Resumo
    total_molas = len(molas_nao_vendidas)
    total_estoque = sum(item['estoque_atual'] for item in molas_nao_vendidas)

    elements.append(Paragraph(f"Total de molas sem vendas: {total_molas}", styles['info']))
    elements.append(Paragraph(f"Estoque total parado: {total_estoque} unidades", styles['info']))

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = f"Molas Não Vendidas - Últimos {periodo_dias} Dias"
        filtros = f"Total: {total_molas} molas"
        add_header_footer(canvas, doc, titulo, filtros)

    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)
    return response


def gerar_csv_molas_nao_vendidas(molas_nao_vendidas, periodo_dias):
    """Gera CSV do relatório de molas não vendidas"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="molas_nao_vendidas_{periodo_dias}_dias.csv"'

    writer = csv.writer(response)

    # Cabeçalho com informação do período
    writer.writerow([f'Molas Não Vendidas - Últimos {periodo_dias} Dias'])
    writer.writerow([f'Total: {len(molas_nao_vendidas)} molas'])
    writer.writerow([])  # Linha em branco

    # Cabeçalho dos dados
    writer.writerow(['Código', 'Cliente', 'Última Venda', 'Dias Sem Venda', 'Estoque Atual'])

    # Dados
    for item in molas_nao_vendidas:
        ultima_venda_str = item['ultima_venda'].strftime('%d/%m/%Y') if item['ultima_venda'] else 'Nunca'
        dias_sem_venda_str = str(item['dias_sem_venda']) if item['dias_sem_venda'] else 'N/A'

        writer.writerow([
            item['mola'].codigo,
            item['mola'].cliente,
            ultima_venda_str,
            dias_sem_venda_str,
            item['estoque_atual']
        ])

    return response


def auditoria_sistema(request):
    """Realiza auditoria completa do sistema"""
    if request.method == 'POST':
        # Executar auditoria
        resultados = executar_auditoria_completa()

        return render(request, 'estoque/auditoria_sistema.html', {
            'resultados': resultados,
            'executada': True
        })

    return render(request, 'estoque/auditoria_sistema.html', {'executada': False})


def executar_auditoria_completa():
    """Executa auditoria completa do sistema e retorna resultados"""
    resultados = {
        'problemas_criticos': [],
        'problemas_medios': [],
        'problemas_menores': [],
        'funcionalidades_redundantes': [],
        'funcionalidades_faltantes': [],
        'estatisticas_gerais': {},
        'recomendacoes': []
    }

    # 1. Verificar consistência dos dados
    verificar_consistencia_dados(resultados)

    # 2. Verificar integridade referencial
    verificar_integridade_referencial(resultados)

    # 3. Verificar precisão dos cálculos
    verificar_precisao_calculos(resultados)

    # 4. Identificar funcionalidades redundantes
    identificar_funcionalidades_redundantes(resultados)

    # 5. Identificar funcionalidades em falta
    identificar_funcionalidades_faltantes(resultados)

    # 6. Coletar estatísticas gerais
    coletar_estatisticas_gerais(resultados)

    # 7. Gerar recomendações
    gerar_recomendacoes(resultados)

    return resultados


def verificar_consistencia_dados(resultados):
    """Verifica consistência dos dados no sistema"""

    # Verificar molas com estoque negativo
    molas_estoque_negativo = Mola.objects.filter(quantidade_estoque__lt=0)
    if molas_estoque_negativo.exists():
        resultados['problemas_criticos'].append({
            'tipo': 'Estoque Negativo',
            'descricao': f'{molas_estoque_negativo.count()} molas com estoque negativo',
            'detalhes': [f'{m.codigo} - {m.cliente}: {m.quantidade_estoque}' for m in molas_estoque_negativo[:10]],
            'acao': 'Corrigir quantidades de estoque'
        })

    # Verificar molas sem código
    molas_sem_codigo = Mola.objects.filter(codigo__isnull=True) | Mola.objects.filter(codigo='')
    if molas_sem_codigo.exists():
        resultados['problemas_criticos'].append({
            'tipo': 'Molas sem Código',
            'descricao': f'{molas_sem_codigo.count()} molas sem código definido',
            'detalhes': [f'ID {m.id} - {m.cliente}' for m in molas_sem_codigo[:10]],
            'acao': 'Definir códigos para todas as molas'
        })

    # Verificar molas duplicadas (mesmo código)
    from django.db.models import Count
    codigos_duplicados = Mola.objects.values('codigo').annotate(
        count=Count('codigo')
    ).filter(count__gt=1)

    if codigos_duplicados.exists():
        resultados['problemas_medios'].append({
            'tipo': 'Códigos Duplicados',
            'descricao': f'{codigos_duplicados.count()} códigos de mola duplicados',
            'detalhes': [f"Código '{item['codigo']}': {item['count']} molas" for item in codigos_duplicados[:10]],
            'acao': 'Revisar e corrigir códigos duplicados'
        })

    # Verificar movimentações com quantidade zero
    movimentacoes_zero = MovimentacaoEstoque.objects.filter(quantidade=0)
    if movimentacoes_zero.exists():
        resultados['problemas_menores'].append({
            'tipo': 'Movimentações com Quantidade Zero',
            'descricao': f'{movimentacoes_zero.count()} movimentações com quantidade zero',
            'detalhes': [f'ID {m.id} - {m.mola.codigo} em {m.data}' for m in movimentacoes_zero[:10]],
            'acao': 'Remover ou corrigir movimentações inválidas'
        })


def verificar_integridade_referencial(resultados):
    """Verifica integridade referencial entre modelos"""

    # Verificar itens de pedido órfãos
    itens_orfaos = ItemPedido.objects.filter(pedido__isnull=True)
    if itens_orfaos.exists():
        resultados['problemas_criticos'].append({
            'tipo': 'Itens de Pedido Órfãos',
            'descricao': f'{itens_orfaos.count()} itens de pedido sem pedido associado',
            'detalhes': [f'Item ID {item.id} - Mola {item.mola.codigo}' for item in itens_orfaos[:10]],
            'acao': 'Corrigir ou remover itens órfãos'
        })

    # Verificar movimentações órfãs
    movimentacoes_orfas = MovimentacaoEstoque.objects.filter(mola__isnull=True)
    if movimentacoes_orfas.exists():
        resultados['problemas_criticos'].append({
            'tipo': 'Movimentações Órfãs',
            'descricao': f'{movimentacoes_orfas.count()} movimentações sem mola associada',
            'detalhes': [f'Movimentação ID {mov.id} em {mov.data}' for mov in movimentacoes_orfas[:10]],
            'acao': 'Corrigir ou remover movimentações órfãs'
        })


def verificar_precisao_calculos(resultados):
    """Verifica precisão dos cálculos do sistema"""

    # Verificar se o estoque calculado bate com as movimentações
    molas_com_divergencia = []

    for mola in Mola.objects.all()[:100]:  # Verificar primeiras 100 molas
        # Calcular estoque baseado nas movimentações
        entradas = MovimentacaoEstoque.objects.filter(mola=mola, tipo='E').aggregate(
            total=Sum('quantidade')
        )['total'] or 0

        saidas = MovimentacaoEstoque.objects.filter(mola=mola, tipo='S').aggregate(
            total=Sum('quantidade')
        )['total'] or 0

        estoque_calculado = entradas - saidas

        if estoque_calculado != mola.quantidade_estoque:
            molas_com_divergencia.append({
                'codigo': mola.codigo,
                'cliente': mola.cliente,
                'estoque_sistema': mola.quantidade_estoque,
                'estoque_calculado': estoque_calculado,
                'diferenca': mola.quantidade_estoque - estoque_calculado
            })

    if molas_com_divergencia:
        resultados['problemas_medios'].append({
            'tipo': 'Divergência de Estoque',
            'descricao': f'{len(molas_com_divergencia)} molas com divergência entre estoque registrado e calculado',
            'detalhes': [f"{m['codigo']} - Sistema: {m['estoque_sistema']}, Calculado: {m['estoque_calculado']}" for m in molas_com_divergencia[:10]],
            'acao': 'Recalcular estoques baseado nas movimentações'
        })


def identificar_funcionalidades_redundantes(resultados):
    """Identifica funcionalidades redundantes no sistema"""

    redundancias = [
        {
            'funcionalidade': 'Múltiplas formas de registrar vendas',
            'descricao': 'Sistema permite vendas via movimentação de estoque e via itens de pedido',
            'impacto': 'Pode causar confusão e inconsistências',
            'recomendacao': 'Padronizar processo de vendas'
        },
        {
            'funcionalidade': 'Campos de material em molas',
            'descricao': 'Molas têm campo material que pode ser redundante com material padrão',
            'impacto': 'Duplicação de informações',
            'recomendacao': 'Revisar necessidade do campo material em molas'
        }
    ]

    resultados['funcionalidades_redundantes'] = redundancias


def identificar_funcionalidades_faltantes(resultados):
    """Identifica funcionalidades que poderiam ser úteis"""

    faltantes = [
        {
            'funcionalidade': 'Backup automático',
            'descricao': 'Sistema não possui backup automático dos dados',
            'prioridade': 'Alta',
            'beneficio': 'Proteção contra perda de dados'
        },
        {
            'funcionalidade': 'Log de auditoria',
            'descricao': 'Não há registro de quem fez quais alterações',
            'prioridade': 'Média',
            'beneficio': 'Rastreabilidade de mudanças'
        },
        {
            'funcionalidade': 'Alertas automáticos',
            'descricao': 'Sistema não envia alertas automáticos por email',
            'prioridade': 'Baixa',
            'beneficio': 'Notificações proativas'
        },
        {
            'funcionalidade': 'Integração com fornecedores',
            'descricao': 'Não há integração com sistemas de fornecedores',
            'prioridade': 'Baixa',
            'beneficio': 'Automatização de compras'
        }
    ]

    resultados['funcionalidades_faltantes'] = faltantes


def coletar_estatisticas_gerais(resultados):
    """Coleta estatísticas gerais do sistema"""

    estatisticas = {
        'total_molas': Mola.objects.count(),
        'total_materiais': Material.objects.count(),
        'total_movimentacoes': MovimentacaoEstoque.objects.count(),
        'total_pedidos': PedidoVenda.objects.count(),
        'total_itens_pedido': ItemPedido.objects.count(),
        'molas_com_estoque': Mola.objects.filter(quantidade_estoque__gt=0).count(),
        'molas_sem_estoque': Mola.objects.filter(quantidade_estoque=0).count(),
        'pedidos_pendentes': PedidoVenda.objects.filter(status='P').count(),
        'itens_nao_atendidos': ItemPedido.objects.filter(atendido=False).count(),
    }

    # Calcular valor total do estoque
    valor_total_estoque = 0
    for mola in Mola.objects.filter(quantidade_estoque__gt=0):
        if mola.peso_unitario:
            custo_unitario = mola.peso_unitario / Decimal('1000')  # Converter gramas para kg
            valor_total_estoque += mola.quantidade_estoque * custo_unitario

    estatisticas['valor_total_estoque'] = float(valor_total_estoque)

    resultados['estatisticas_gerais'] = estatisticas


def gerar_recomendacoes(resultados):
    """Gera recomendações baseadas na auditoria"""

    recomendacoes = []

    # Recomendações baseadas nos problemas encontrados
    if resultados['problemas_criticos']:
        recomendacoes.append({
            'prioridade': 'CRÍTICA',
            'titulo': 'Corrigir problemas críticos imediatamente',
            'descricao': 'Foram encontrados problemas críticos que podem afetar o funcionamento do sistema',
            'acao': 'Revisar e corrigir todos os problemas críticos listados'
        })

    if resultados['problemas_medios']:
        recomendacoes.append({
            'prioridade': 'ALTA',
            'titulo': 'Resolver problemas de integridade de dados',
            'descricao': 'Problemas de integridade podem causar inconsistências nos relatórios',
            'acao': 'Implementar rotinas de verificação e correção automática'
        })

    # Recomendações gerais
    recomendacoes.extend([
        {
            'prioridade': 'MÉDIA',
            'titulo': 'Implementar backup automático',
            'descricao': 'Proteger dados contra perda acidental',
            'acao': 'Configurar backup diário automático do banco de dados'
        },
        {
            'prioridade': 'MÉDIA',
            'titulo': 'Padronizar processo de vendas',
            'descricao': 'Unificar forma de registrar vendas para evitar confusão',
            'acao': 'Definir processo único para registro de vendas'
        },
        {
            'prioridade': 'BAIXA',
            'titulo': 'Implementar log de auditoria',
            'descricao': 'Rastrear alterações feitas no sistema',
            'acao': 'Adicionar logging de todas as operações importantes'
        }
    ])

    resultados['recomendacoes'] = recomendacoes


def relatorio_recomendacoes_completo(request):
    """Gera relatório completo de recomendações do sistema"""

    recomendacoes = {
        'criticas': [
            {
                'titulo': 'Implementação de Backup Automático',
                'descricao': 'Sistema atualmente não possui backup automático dos dados',
                'impacto': 'Risco de perda total de dados em caso de falha',
                'implementacao': [
                    'Backup diário automático do banco de dados',
                    'Armazenamento em local seguro (nuvem + local)',
                    'Teste de restauração mensal'
                ],
                'beneficio': 'Proteção completa contra perda de dados',
                'tempo_estimado': '2-3 dias',
                'prioridade': 1
            },
            {
                'titulo': 'Padronização do Processo de Vendas',
                'descricao': 'Atualmente existem duas formas de registrar vendas (movimentação + pedidos)',
                'impacto': 'Confusão operacional e possíveis inconsistências',
                'implementacao': [
                    'Definir processo único para vendas',
                    'Migrar dados históricos para formato padronizado',
                    'Treinar usuários no novo processo'
                ],
                'beneficio': 'Consistência e simplicidade operacional',
                'tempo_estimado': '3-5 dias',
                'prioridade': 2
            }
        ],
        'altas': [
            {
                'titulo': 'Sistema de Log de Auditoria',
                'descricao': 'Implementar rastreamento de todas as alterações importantes',
                'impacto': 'Rastreabilidade e responsabilização',
                'implementacao': [
                    'Log de criação, edição e exclusão de registros',
                    'Identificação do usuário responsável',
                    'Timestamp de todas as operações'
                ],
                'beneficio': 'Controle total sobre mudanças no sistema',
                'tempo_estimado': '4-6 dias',
                'prioridade': 3
            },
            {
                'titulo': 'Melhorias nos Relatórios de Análise',
                'descricao': 'Expandir capacidades analíticas dos relatórios',
                'implementacao': [
                    'Relatório de lucratividade por mola',
                    'Análise de sazonalidade de vendas',
                    'Relatório de performance de fornecedores',
                    'Dashboard executivo com KPIs principais'
                ],
                'beneficio': 'Melhor tomada de decisão baseada em dados',
                'tempo_estimado': '5-7 dias',
                'prioridade': 4
            },
            {
                'titulo': 'Sistema de Alertas Inteligentes',
                'descricao': 'Alertas automáticos para situações críticas',
                'implementacao': [
                    'Alerta de estoque baixo personalizado por mola',
                    'Alerta de itens obsoletos',
                    'Alerta de pedidos em atraso',
                    'Notificações por email (opcional)'
                ],
                'beneficio': 'Gestão proativa do estoque',
                'tempo_estimado': '3-4 dias',
                'prioridade': 5
            }
        ],
        'medias': [
            {
                'titulo': 'Otimização de Performance',
                'descricao': 'Melhorar velocidade e responsividade do sistema',
                'implementacao': [
                    'Otimização de consultas ao banco de dados',
                    'Implementação de cache inteligente',
                    'Compressão de imagens e assets',
                    'Lazy loading em listas grandes'
                ],
                'beneficio': 'Sistema mais rápido e eficiente',
                'tempo_estimado': '4-5 dias',
                'prioridade': 6
            },
            {
                'titulo': 'Interface Mobile-Friendly',
                'descricao': 'Adaptar interface para dispositivos móveis',
                'implementacao': [
                    'Design responsivo completo',
                    'Otimização para touch screens',
                    'Funcionalidades essenciais em mobile'
                ],
                'beneficio': 'Acesso ao sistema de qualquer lugar',
                'tempo_estimado': '6-8 dias',
                'prioridade': 7
            },
            {
                'titulo': 'Sistema de Categorização Avançada',
                'descricao': 'Melhorar organização e busca de molas',
                'implementacao': [
                    'Categorias e subcategorias de molas',
                    'Tags personalizáveis',
                    'Busca avançada com filtros múltiplos',
                    'Favoritos e listas personalizadas'
                ],
                'beneficio': 'Organização e localização mais eficiente',
                'tempo_estimado': '4-6 dias',
                'prioridade': 8
            }
        ],
        'baixas': [
            {
                'titulo': 'Integração com Fornecedores',
                'descricao': 'Automatizar processo de compras',
                'implementacao': [
                    'API para integração com fornecedores',
                    'Cotação automática de preços',
                    'Pedidos de compra automáticos'
                ],
                'beneficio': 'Redução de trabalho manual',
                'tempo_estimado': '10-15 dias',
                'prioridade': 9
            },
            {
                'titulo': 'Módulo de Qualidade',
                'descricao': 'Controle de qualidade de produtos',
                'implementacao': [
                    'Registro de inspeções',
                    'Controle de lotes',
                    'Rastreabilidade completa'
                ],
                'beneficio': 'Garantia de qualidade',
                'tempo_estimado': '8-10 dias',
                'prioridade': 10
            }
        ]
    }

    # Estatísticas do sistema
    estatisticas = {
        'total_molas': Mola.objects.count(),
        'total_materiais': Material.objects.count(),
        'total_movimentacoes': MovimentacaoEstoque.objects.count(),
        'total_pedidos': PedidoVenda.objects.count(),
        'data_geracao': timezone.now().date()
    }

    # Roadmap de implementação
    roadmap = {
        'fase1': {
            'nome': 'Imediata (1-2 semanas)',
            'itens': ['Backup automático', 'Padronização de vendas', 'Correções críticas']
        },
        'fase2': {
            'nome': 'Curto prazo (1 mês)',
            'itens': ['Log de auditoria', 'Melhorias nos relatórios', 'Sistema de alertas', 'Otimização de performance']
        },
        'fase3': {
            'nome': 'Médio prazo (2-3 meses)',
            'itens': ['Interface mobile', 'Sistema de categorização', 'Melhorias de UX/UI', 'Controle de acesso']
        },
        'fase4': {
            'nome': 'Longo prazo (6 meses)',
            'itens': ['Integração com fornecedores', 'Módulo de qualidade', 'Relatórios financeiros', 'Dashboard personalizado']
        }
    }

    return render(request, 'estoque/relatorio_recomendacoes.html', {
        'recomendacoes': recomendacoes,
        'estatisticas': estatisticas,
        'roadmap': roadmap
    })
